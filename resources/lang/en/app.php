<?php

return [
    // Added translations for save points record
     'save_points_record' => 'Save Points Record',
     'points_record_saved' => 'Points record saved successfully',
     'points_history' => 'Points History',
     'saved_records' => 'Saved Records',
     'gameweek' => 'Gameweek',
     'calculated_at' => 'Calculated At',
     'actions' => 'Actions',
     'no_records_found' => 'No records found',
     'save_records_message' => 'Calculate and save points to view them here',
     'go_to_calculator' => 'Go to Calculator',
     'record_details' => 'Record Details',
     'close' => 'Close',
     'performance_stats' => 'Performance Stats',
     'points_breakdown' => 'Points Breakdown',
    // Navigation
    'home' => 'Home',
    'calculator' => 'Points Calculator',
    'rules' => 'Rules',
    'language' => 'Language',
    'points_calculator' => 'Points Calculator',
    
    // Points Calculator
    'gameweek_points' => 'Gameweek Points',
    'enter_points' => 'Enter points',
    'opponent_players' => 'Opponent Players',
    'enter_number' => 'Enter number',
    'more_than_four' => 'More than 4 players?',
    'more_than_four_penalty' => 'More than 4 players penalty',
    'team_result' => 'Team Result',
    'win' => 'Win',
    'draw' => 'Draw',
    'loss' => 'Loss',
    'win_bonus' => 'Win bonus',
    'draw_penalty' => 'Draw penalty',
    'loss_penalty' => 'Loss penalty',
    'calculate' => 'Calculate',
    'reset' => 'Reset',
    'results' => 'Results',
    'total_points' => 'Total Points',
    
    // Home Page
    'welcome_title' => 'Fantasy Premier League - Official Fantasy Football Game',
    'welcome_description' => 'Build your dream team and compete with millions of players worldwide',
    'features' => 'Features',
    'news' => 'News',
    'about' => 'About',
    'register_now' => 'Register Now',
    'watch_demo' => 'Watch Demo',
    'learn_more' => 'Learn More',

    // Features
    'features_title' => 'Why Choose Fantasy Premier League?',
    'features_subtitle' => 'Experience the ultimate fantasy football game with cutting-edge features',

    // News
    'latest_news' => 'Latest Premier League News',
    'news_subtitle' => 'Stay updated with AI-powered news and insights from the Premier League',
    'read_more' => 'Read More',
    'load_more_news' => 'Load More News',
    'loading' => 'Loading',

    // CTA
    'ready_to_play' => 'Ready to Play?',
    'register_description' => 'Join millions of players worldwide and start building your dream team today!',
    'demo_video' => 'Demo Video',

    // Footer
    'footer_description' => 'The official fantasy football game of the Premier League. Build your dream team and compete with millions.',
    'quick_links' => 'Quick Links',
    'support' => 'Support',
    'help_center' => 'Help Center',
    'contact_us' => 'Contact Us',
    'faq' => 'FAQ',
    'privacy_policy' => 'Privacy Policy',
    'newsletter' => 'Newsletter',
    'newsletter_description' => 'Get the latest news and updates delivered to your inbox.',
    'enter_email' => 'Enter your email',
    'subscribe' => 'Subscribe',
    'all_rights_reserved' => 'All rights reserved.',
    'made_with' => 'Made with',
    'for_football_fans' => 'for football fans worldwide',

    // FPL Connection
    'connect_fpl_account' => 'Connect Fantasy Premier League Account',
    'connect_fpl_description' => 'Connect your Fantasy Premier League account and get your stats and team data directly',
    'enter_fpl_id' => 'Enter Manager ID',
    'fpl_team_id' => 'Manager ID',
    'enter_team_id' => 'Example: 123456',
    'connect_account' => 'Connect Account',
    'fpl_id_help' => 'Your data is safe and we won\'t access your personal information',

    // Profile
    'my_profile' => 'My Profile',
    'total_points' => 'Total Points',
    'overall_rank' => 'Overall Rank',
    'gameweek_points' => 'Gameweek Points',
    'team_value' => 'Team Value',
    'bank_value' => 'Bank',
    'current_gameweek' => 'Current Gameweek',
    'team_formation' => 'Team Formation',
    'update_data' => 'Update Data',
    'last_updated' => 'Last Updated',

    // Instructions
    'go_to_fpl_website' => 'Go to fantasy.premierleague.com',
    'login_to_account' => 'Login to your account',
    'click_points_tab' => 'Click on "Points" tab',
    'copy_id_from_url' => 'Copy the ID from URL (example: /entry/123456/event/1)',
    'not_specified' => 'Not specified',
    'register' => 'Register',
    'get_started' => 'Get Started',
    'how_it_works' => 'How It Works',
    'step_1' => 'Enter Player Stats',
    'step_1_desc' => 'Input the player\'s performance statistics',
    'step_2' => 'Automatic Calculation',
    'step_2_desc' => 'Points are calculated instantly based on FPL rules',
    'step_3' => 'View Results',
    'step_3_desc' => 'See detailed breakdown of points earned',
    
    // Calculator Page
    'points_calculator' => 'Points Calculator',
    'player_info' => 'Player Information',
    'player_name' => 'Player Name',
    'player_position' => 'Position',
    'select_position' => 'Select Position',
    'goalkeeper' => 'Goalkeeper',
    'defender' => 'Defender',
    'midfielder' => 'Midfielder',
    'forward' => 'Forward',
    
    // Statistics
    'performance_stats' => 'Performance Statistics',
    'goals' => 'Goals',
    'assists' => 'Assists',
    'clean_sheets' => 'Clean Sheets',
    'goals_conceded' => 'Goals Conceded',
    'saves' => 'Saves',
    'penalty_saves' => 'Penalty Saves',
    'penalty_misses' => 'Penalty Misses',
    'yellow_cards' => 'Yellow Cards',
    'red_cards' => 'Red Cards',
    'own_goals' => 'Own Goals',
    'minutes_played' => 'Minutes Played',
    
    // Results
    'results' => 'Results',
    'total_points' => 'Total Points',
    'points_breakdown' => 'Points Breakdown',
    'calculate' => 'Calculate Points',
    'reset' => 'Reset',
    'clear_all' => 'Clear All',
    
    // Points Breakdown
    'appearance_points' => 'Appearance Points',
    'goal_points' => 'Goal Points',
    'assist_points' => 'Assist Points',
    'clean_sheet_points' => 'Clean Sheet Points',
    'save_points' => 'Save Points',
    'penalty_save_points' => 'Penalty Save Points',
    'yellow_card_points' => 'Yellow Card Points',
    'red_card_points' => 'Red Card Points',
    'own_goal_points' => 'Own Goal Points',
    'penalty_miss_points' => 'Penalty Miss Points',
    
    // Rules Page
    'scoring_rules' => 'Scoring Rules',
    'basic_scoring' => 'Basic Scoring',
    'position_specific' => 'Position Specific Rules',
    'penalties' => 'Penalties',
    'appearance_rule' => 'Playing 1-59 minutes: +1 point',
    'appearance_rule_full' => 'Playing 60+ minutes: +2 points',
    
    // Goalkeeper Rules
    'gk_rules' => 'Goalkeeper Rules',
    'gk_goal' => 'Goal: +6 points',
    'gk_assist' => 'Assist: +3 points',
    'gk_clean_sheet' => 'Clean Sheet: +4 points',
    'gk_saves' => 'Every 3 saves: +1 point',
    'gk_penalty_save' => 'Penalty Save: +5 points',
    'gk_goals_conceded' => 'Every 2 goals conceded: -1 point',
    
    // Defender Rules
    'def_rules' => 'Defender Rules',
    'def_goal' => 'Goal: +6 points',
    'def_assist' => 'Assist: +3 points',
    'def_clean_sheet' => 'Clean Sheet: +4 points',
    'def_goals_conceded' => 'Every 2 goals conceded: -1 point',
    
    // Midfielder Rules
    'mid_rules' => 'Midfielder Rules',
    'mid_goal' => 'Goal: +5 points',
    'mid_assist' => 'Assist: +3 points',
    'mid_clean_sheet' => 'Clean Sheet: +1 point',
    
    // Forward Rules
    'fwd_rules' => 'Forward Rules',
    'fwd_goal' => 'Goal: +4 points',
    'fwd_assist' => 'Assist: +3 points',
    
    // Common Penalties
    'common_penalties' => 'Common Penalties',
    'yellow_card_penalty' => 'Yellow Card: -1 point',
    'red_card_penalty' => 'Red Card: -3 points',
    'own_goal_penalty' => 'Own Goal: -2 points',
    'penalty_miss_penalty' => 'Penalty Miss: -2 points',
    
    // Footer
    'footer_text' => 'Fantasy Premier League Points Calculator',
    'footer_desc' => 'Calculate player points according to official FPL rules',
    
    // Buttons
    'back_to_calculator' => 'Back to Calculator',
    'view_rules' => 'View Rules',
    
    // Messages
    'enter_player_name' => 'Please enter player name',
    'select_player_position' => 'Please select player position',
    'calculation_complete' => 'Calculation completed successfully',
    'invalid_input' => 'Please check your input values',

    // FPL Connection
    'connect_fpl_account' => 'Connect Fantasy Premier League Account',
    'connect_fpl_description' => 'Connect your official Fantasy Premier League account to fetch your team data and calculate points using your custom rules',
    'enter_fpl_id' => 'Enter Your Team ID',
    'fpl_team_id' => 'FPL Team ID',
    'enter_team_id' => 'Enter your team ID (e.g., 1234567)',
    'fpl_id_help' => 'You can find your team ID in the URL of your team page on the official Fantasy Premier League website',
    'connect_account' => 'Connect Account',
    'connecting' => 'Connecting',

    // How to Find ID
    'how_to_find_id' => 'How to Find Your Team ID',
    'step_1_title' => 'Go to FPL Website',
    'step_1_desc' => 'Visit fantasy.premierleague.com and log in to your account',
    'step_2_title' => 'Go to Your Team Page',
    'step_2_desc' => 'Click on "My Team" or "Points" in the top navigation',
    'step_3_title' => 'Copy ID from URL',
    'step_3_desc' => 'You\'ll find your team ID in the page URL after /entry/',
    'step_4_title' => 'Paste ID Here',
    'step_4_desc' => 'Copy the number and paste it in the field above',
    'example' => 'Example',
    'id_example' => 'If the URL is: fantasy.premierleague.com/entry/1234567/event/1 then your team ID is: 1234567',

    // Features
    'what_you_get' => 'What You Get',
    'custom_points' => 'Custom Points',
    'custom_points_desc' => 'Calculate your players\' points using your own custom rules',
    'real_time_data' => 'Real-time Data',
    'real_time_desc' => 'Updated data from the official Fantasy Premier League website',
    'comparison' => 'Points Comparison',
    'comparison_desc' => 'Compare official points with your custom scoring system',
    'team_analysis' => 'Team Analysis',
    'team_analysis_desc' => 'Comprehensive analysis of your team performance and ranking',

    // Dashboard
    'dashboard' => 'Dashboard',
    'my_team' => 'My Team',
    'official_points' => 'Official Points',
    'custom_points_total' => 'Custom Points',
    'points_difference' => 'Points Difference',
    'current_gameweek' => 'Current Gameweek',
    'disconnect_account' => 'Disconnect Account',
    'team_value' => 'Team Value',
    'overall_rank' => 'Overall Rank',
];
