<?php

return [
    // Added translations for save points record
     'save_points_record' => 'Enregistrer l\'historique des points',
     'points_record_saved' => 'L\'historique des points a été enregistré avec succès',
     'points_history' => 'Historique des points',
     'saved_records' => 'Enregistrements sauvegardés',
     'gameweek' => 'Journée',
     'calculated_at' => 'Calculé le',
     'actions' => 'Actions',
     'no_records_found' => 'Aucun enregistrement trouvé',
     'save_records_message' => 'Calculez et sauvegardez les points pour les voir ici',
     'go_to_calculator' => 'Aller au calculateur',
     'record_details' => 'Détails de l\'enregistrement',
     'close' => 'Fermer',
     'performance_stats' => 'Statistiques de performance',
     'points_breakdown' => 'Répartition des points',
    // Navigation
    'home' => 'Accueil',
    'calculator' => 'Calculateur de Points',
    'rules' => 'Règles',
    'language' => 'Langue',
    'points_calculator' => 'Calculateur de Points',
    
    // Points Calculator
    'gameweek_points' => 'Points de la journée',
    'enter_points' => 'Entrez les points',
    'opponent_players' => 'Joueurs adverses',
    'enter_number' => 'Entrez le nombre',
    'more_than_four' => 'Plus de 4 joueurs?',
    'more_than_four_penalty' => 'Pénalité pour plus de 4 joueurs',
    'team_result' => 'Résultat de l\'équipe',
    'win' => 'Victoire',
    'draw' => 'Match nul',
    'loss' => 'Défaite',
    'win_bonus' => 'Bonus de victoire',
    'draw_penalty' => 'Pénalité de match nul',
    'loss_penalty' => 'Pénalité de défaite',
    'calculate' => 'Calculer',
    'reset' => 'Réinitialiser',
    'results' => 'Résultats',
    'total_points' => 'Total des points',
    
    // Home Page
    'welcome_title' => 'Fantasy Premier League - Jeu de Football Fantasy Officiel',
    'welcome_description' => 'Construisez l\'équipe de vos rêves et rivalisez avec des millions de joueurs dans le monde',
    'features' => 'Fonctionnalités',
    'news' => 'Actualités',
    'about' => 'À propos',
    'register_now' => 'S\'inscrire maintenant',
    'watch_demo' => 'Regarder la démo',
    'learn_more' => 'En savoir plus',

    // Features
    'features_title' => 'Pourquoi choisir Fantasy Premier League?',
    'features_subtitle' => 'Découvrez le jeu de football fantasy ultime avec des fonctionnalités de pointe',

    // News
    'latest_news' => 'Dernières actualités de la Premier League',
    'news_subtitle' => 'Restez informé avec des actualités et des analyses alimentées par l\'IA de la Premier League',
    'read_more' => 'Lire la suite',
    'load_more_news' => 'Charger plus d\'actualités',
    'loading' => 'Chargement',

    // CTA
    'ready_to_play' => 'Prêt à jouer?',
    'register_description' => 'Rejoignez des millions de joueurs dans le monde et commencez à construire l\'équipe de vos rêves dès aujourd\'hui!',
    'demo_video' => 'Vidéo de démonstration',

    // Footer
    'footer_description' => 'Le jeu de football fantasy officiel de la Premier League. Construisez l\'équipe de vos rêves et rivalisez avec des millions.',
    'quick_links' => 'Liens rapides',
    'support' => 'Support',
    'help_center' => 'Centre d\'aide',
    'contact_us' => 'Nous contacter',
    'faq' => 'FAQ',
    'privacy_policy' => 'Politique de confidentialité',
    'newsletter' => 'Newsletter',
    'newsletter_description' => 'Recevez les dernières nouvelles et mises à jour dans votre boîte de réception.',
    'enter_email' => 'Entrez votre email',
    'subscribe' => 'S\'abonner',
    'all_rights_reserved' => 'Tous droits réservés.',
    'made_with' => 'Fait avec',
    'for_football_fans' => 'pour les fans de football du monde entier',

    // FPL Connection
    'connect_fpl_account' => 'Connecter le compte Fantasy Premier League',
    'connect_fpl_description' => 'Connectez votre compte Fantasy Premier League et obtenez vos statistiques et données d\'équipe directement',
    'enter_fpl_id' => 'Entrez l\'ID du Manager',
    'fpl_team_id' => 'ID du Manager',
    'enter_team_id' => 'Exemple: 123456',
    'connect_account' => 'Connecter le compte',
    'fpl_id_help' => 'Vos données sont sécurisées et nous n\'accéderons pas à vos informations personnelles',

    // Profile
    'my_profile' => 'Mon Profil',
    'total_points' => 'Points Totaux',
    'overall_rank' => 'Classement Général',
    'gameweek_points' => 'Points de la Journée',
    'team_value' => 'Valeur de l\'Équipe',
    'bank_value' => 'Banque',
    'current_gameweek' => 'Journée Actuelle',
    'team_formation' => 'Formation de l\'Équipe',
    'update_data' => 'Mettre à jour les données',
    'last_updated' => 'Dernière mise à jour',

    // Instructions
    'go_to_fpl_website' => 'Allez sur fantasy.premierleague.com',
    'login_to_account' => 'Connectez-vous à votre compte',
    'click_points_tab' => 'Cliquez sur l\'onglet "Points"',
    'copy_id_from_url' => 'Copiez l\'ID depuis l\'URL (exemple: /entry/123456/event/1)',
    'not_specified' => 'Non spécifié',

    // FPL Connection
    'connect_fpl_account' => 'Connecter le compte Fantasy Premier League',
    'connect_fpl_description' => 'Connectez votre compte Fantasy Premier League et obtenez vos statistiques et données d\'équipe directement',
    'enter_fpl_id' => 'Entrez l\'ID du Manager',
    'fpl_team_id' => 'ID du Manager',
    'enter_team_id' => 'Exemple: 123456',
    'connect_account' => 'Connecter le compte',
    'fpl_id_help' => 'Vos données sont sécurisées et nous n\'accéderons pas à vos informations personnelles',

    // Profile
    'my_profile' => 'Mon Profil',
    'total_points' => 'Points Totaux',
    'overall_rank' => 'Classement Général',
    'gameweek_points' => 'Points de la Journée',
    'team_value' => 'Valeur de l\'Équipe',
    'bank_value' => 'Banque',
    'current_gameweek' => 'Journée Actuelle',
    'team_formation' => 'Formation de l\'Équipe',
    'update_data' => 'Mettre à jour les données',
    'last_updated' => 'Dernière mise à jour',
    'register' => 'S\'inscrire',
    'get_started' => 'Commencer',
    'how_it_works' => 'Comment ça marche',
    'step_1' => 'Entrez les Stats du Joueur',
    'step_1_desc' => 'Saisissez les statistiques de performance du joueur',
    'step_2' => 'Calcul Automatique',
    'step_2_desc' => 'Les points sont calculés instantanément selon les règles FPL',
    'step_3' => 'Voir les Résultats',
    'step_3_desc' => 'Consultez le détail des points gagnés',
    
    // Calculator Page
    'points_calculator' => 'Calculateur de Points',
    'player_info' => 'Informations du Joueur',
    'player_name' => 'Nom du Joueur',
    'player_position' => 'Position',
    'select_position' => 'Sélectionner Position',
    'goalkeeper' => 'Gardien',
    'defender' => 'Défenseur',
    'midfielder' => 'Milieu',
    'forward' => 'Attaquant',
    
    // Statistics
    'performance_stats' => 'Statistiques de Performance',
    'goals' => 'Buts',
    'assists' => 'Passes Décisives',
    'clean_sheets' => 'Clean Sheets',
    'goals_conceded' => 'Buts Encaissés',
    'saves' => 'Arrêts',
    'penalty_saves' => 'Arrêts de Penalty',
    'penalty_misses' => 'Penalties Ratés',
    'yellow_cards' => 'Cartons Jaunes',
    'red_cards' => 'Cartons Rouges',
    'own_goals' => 'Buts Contre Son Camp',
    'minutes_played' => 'Minutes Jouées',
    
    // Results
    'results' => 'Résultats',
    'total_points' => 'Total Points',
    'points_breakdown' => 'Détail des Points',
    'calculate' => 'Calculer Points',
    'reset' => 'Réinitialiser',
    'clear_all' => 'Tout Effacer',
    
    // Points Breakdown
    'appearance_points' => 'Points de Participation',
    'goal_points' => 'Points de Buts',
    'assist_points' => 'Points de Passes',
    'clean_sheet_points' => 'Points Clean Sheet',
    'save_points' => 'Points d\'Arrêts',
    'penalty_save_points' => 'Points Arrêt Penalty',
    'yellow_card_points' => 'Points Carton Jaune',
    'red_card_points' => 'Points Carton Rouge',
    'own_goal_points' => 'Points But Contre Son Camp',
    'penalty_miss_points' => 'Points Penalty Raté',
    
    // Rules Page
    'scoring_rules' => 'Règles de Notation',
    'basic_scoring' => 'Notation de Base',
    'position_specific' => 'Règles Spécifiques par Position',
    'penalties' => 'Pénalités',
    'appearance_rule' => 'Jouer 1-59 minutes: +1 point',
    'appearance_rule_full' => 'Jouer 60+ minutes: +2 points',
    
    // Goalkeeper Rules
    'gk_rules' => 'Règles Gardien',
    'gk_goal' => 'But: +6 points',
    'gk_assist' => 'Passe Décisive: +3 points',
    'gk_clean_sheet' => 'Clean Sheet: +4 points',
    'gk_saves' => 'Chaque 3 arrêts: +1 point',
    'gk_penalty_save' => 'Arrêt Penalty: +5 points',
    'gk_goals_conceded' => 'Chaque 2 buts encaissés: -1 point',
    
    // Defender Rules
    'def_rules' => 'Règles Défenseur',
    'def_goal' => 'But: +6 points',
    'def_assist' => 'Passe Décisive: +3 points',
    'def_clean_sheet' => 'Clean Sheet: +4 points',
    'def_goals_conceded' => 'Chaque 2 buts encaissés: -1 point',
    
    // Midfielder Rules
    'mid_rules' => 'Règles Milieu',
    'mid_goal' => 'But: +5 points',
    'mid_assist' => 'Passe Décisive: +3 points',
    'mid_clean_sheet' => 'Clean Sheet: +1 point',
    
    // Forward Rules
    'fwd_rules' => 'Règles Attaquant',
    'fwd_goal' => 'But: +4 points',
    'fwd_assist' => 'Passe Décisive: +3 points',
    
    // Common Penalties
    'common_penalties' => 'Pénalités Communes',
    'yellow_card_penalty' => 'Carton Jaune: -1 point',
    'red_card_penalty' => 'Carton Rouge: -3 points',
    'own_goal_penalty' => 'But Contre Son Camp: -2 points',
    'penalty_miss_penalty' => 'Penalty Raté: -2 points',
    
    // Footer
    'footer_text' => 'Calculateur de Points Fantasy Premier League',
    'footer_desc' => 'Calculez les points des joueurs selon les règles officielles FPL',
    
    // Buttons
    'back_to_calculator' => 'Retour au Calculateur',
    'view_rules' => 'Voir les Règles',
    
    // Messages
    'enter_player_name' => 'Veuillez entrer le nom du joueur',
    'select_player_position' => 'Veuillez sélectionner la position du joueur',
    'calculation_complete' => 'Calcul terminé avec succès',
    'invalid_input' => 'Veuillez vérifier vos valeurs saisies',
];
