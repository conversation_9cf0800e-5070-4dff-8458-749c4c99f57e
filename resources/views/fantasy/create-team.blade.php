@extends('layouts.fpl')

@section('title', 'إنشاء فريق جديد - Fantasy Premier League')

@section('content')
<!-- Hero Section -->
<section class="fpl-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1>إنشاء فريق جديد</h1>
                <p>اختر أفضل 15 لاعباً لفريقك (11 أساسي + 4 احتياط)</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <div class="btn btn-fpl-secondary btn-lg" id="budgetDisplay">
                        <i class="fas fa-money-bill me-2"></i>
                        الميزانية: 100.0 مليون
                    </div>
                    <div class="btn btn-outline-light btn-lg" id="playersCount">
                        <i class="fas fa-users me-2"></i>
                        اللاعبين: 0/15
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">

    <form id="teamForm" onsubmit="return submitTeamForm(event);">
        @csrf
        
        <!-- Team Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="fpl-card">
                    <div class="fpl-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الفريق
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الفريق</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="manager_name" class="form-label">اسم المدير</label>
                            <input type="text" class="form-control" id="manager_name" name="manager_name" required>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="fpl-card">
                    <div class="fpl-card-header" style="background: var(--fpl-gradient-secondary); color: var(--fpl-dark);">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            إحصائيات الفريق
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="team-value" id="totalValue">0.0 مليون</div>
                                <small class="text-muted">القيمة الإجمالية</small>
                            </div>
                            <div class="col-6">
                                <div class="badge bg-info fs-6" id="selectedCount">0/15</div>
                                <small class="text-muted d-block">اللاعبين المختارين</small>
                            </div>
                        </div>
                        <div class="progress mt-3">
                            <div class="progress-bar" id="valueProgress" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">الحد الأقصى: 100 مليون</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Formation Selection -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-chess-board me-2"></i>
                    اختيار التشكيلة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">المدافعين</label>
                        <select class="form-select" id="defCount" name="formation[def]">
                            <option value="3">3</option>
                            <option value="4" selected>4</option>
                            <option value="5">5</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">خط الوسط</label>
                        <select class="form-select" id="midCount" name="formation[mid]">
                            <option value="3">3</option>
                            <option value="4" selected>4</option>
                            <option value="5">5</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">المهاجمين</label>
                        <select class="form-select" id="fwdCount" name="formation[fwd]">
                            <option value="1">1</option>
                            <option value="2" selected>2</option>
                            <option value="3">3</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الحراس</label>
                        <input type="text" class="form-control" value="1" readonly>
                        <input type="hidden" name="formation[gk]" value="1">
                    </div>
                </div>
            </div>
        </div>

        <!-- Players Selection -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-user-friends me-2"></i>
                    اختيار اللاعبين
                </h5>
            </div>
            <div class="card-body">
                <!-- Filter -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-select" id="teamFilter">
                            <option value="">جميع الفرق</option>
                            @foreach($teams as $team)
                                <option value="{{ $team->id }}">{{ $team->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="positionFilter">
                            <option value="">جميع المراكز</option>
                            <option value="GK">حراس المرمى</option>
                            <option value="DEF">مدافعين</option>
                            <option value="MID">خط الوسط</option>
                            <option value="FWD">مهاجمين</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="nameFilter" placeholder="البحث بالاسم...">
                    </div>
                </div>

                <!-- Players Container -->
                <div id="playersContainer">
                    <div class="text-center py-5">
                        <div class="loading-spinner mb-3"></div>
                        <h5>جاري تحميل اللاعبين...</h5>
                    </div>
                </div>
            </div>
        </div>

        <!-- Captain Selection -->
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-crown me-2"></i>
                    اختيار الكابتن ونائب الكابتن
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="captain" class="form-label">الكابتن (نقاط مضاعفة)</label>
                        <select class="form-select" id="captain" name="captain" required>
                            <option value="">اختر الكابتن</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="vice_captain" class="form-label">نائب الكابتن</label>
                        <select class="form-select" id="vice_captain" name="vice_captain" required>
                            <option value="">اختر نائب الكابتن</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="text-center">
            <button type="submit" class="btn btn-fpl-primary btn-lg px-5" id="submitBtn">
                <i class="fas fa-save me-2"></i>
                إنشاء الفريق
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    let selectedPlayers = [];
    let totalValue = 0;
    let maxBudget = 100;
    let allPlayers = [];

    // تحميل اللاعبين عند بدء الصفحة
    loadPlayers();

    // تحديث العرض الديناميكي
    function updateDisplay() {
        $('#budgetDisplay').html(`<i class="fas fa-money-bill me-2"></i>الميزانية: ${(maxBudget - totalValue).toFixed(1)} مليون`);
        $('#playersCount').html(`<i class="fas fa-users me-2"></i>اللاعبين: ${selectedPlayers.length}/15`);

        // تحديث الألوان حسب الحالة
        if (totalValue > maxBudget) {
            $('#budgetDisplay').removeClass('btn-fpl-secondary').addClass('btn-danger');
        } else if (totalValue > maxBudget * 0.9) {
            $('#budgetDisplay').removeClass('btn-fpl-secondary btn-danger').addClass('btn-warning');
        } else {
            $('#budgetDisplay').removeClass('btn-danger btn-warning').addClass('btn-fpl-secondary');
        }

        if (selectedPlayers.length === 15) {
            $('#playersCount').removeClass('btn-outline-light').addClass('btn-success');
        } else {
            $('#playersCount').removeClass('btn-success').addClass('btn-outline-light');
        }
    }

    // تحميل اللاعبين من API
    function loadPlayers() {
        $.ajax({
            url: '{{ route("api.players") }}',
            method: 'GET',
            success: function(players) {
                allPlayers = players;
                displayPlayers(players);
                populateFilters(players);
            },
            error: function() {
                showAlert('حدث خطأ في تحميل اللاعبين', 'danger');
            }
        });
    }

    // عرض اللاعبين
    function displayPlayers(players) {
        let playersHtml = '';

        // تجميع اللاعبين حسب المركز
        const positions = {
            'GK': { name: 'حراس المرمى', players: [] },
            'DEF': { name: 'المدافعون', players: [] },
            'MID': { name: 'خط الوسط', players: [] },
            'FWD': { name: 'المهاجمون', players: [] }
        };

        players.forEach(player => {
            if (positions[player.position]) {
                positions[player.position].players.push(player);
            }
        });

        Object.keys(positions).forEach(posKey => {
            const pos = positions[posKey];
            if (pos.players.length > 0) {
                playersHtml += `
                    <div class="mb-4">
                        <h4 class="fw-bold mb-3" style="color: var(--fpl-purple);">
                            <i class="fas fa-users me-2"></i>
                            ${pos.name} (${pos.players.length})
                        </h4>
                        <div class="row">
                `;

                pos.players.forEach(player => {
                    const isSelected = selectedPlayers.some(p => p.id == player.id);
                    playersHtml += `
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3 player-item"
                             data-team="${player.team_id}"
                             data-position="${player.position}"
                             data-name="${player.name.toLowerCase()}">
                            <div class="fpl-card player-card h-100 ${isSelected ? 'selected' : ''}"
                                 data-player-id="${player.id}">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0 fw-bold">${player.name}</h6>
                                        <div class="form-check">
                                            <input class="form-check-input player-checkbox"
                                                   type="checkbox"
                                                   value="${player.id}"
                                                   data-price="${player.price}"
                                                   data-position="${player.position}"
                                                   ${isSelected ? 'checked' : ''}>
                                        </div>
                                    </div>

                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        ${player.team}
                                    </p>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold text-success">
                                            ${player.price}م
                                        </span>
                                        <span class="badge bg-primary">
                                            ${player.points} نقطة
                                        </span>
                                    </div>

                                    <div class="mt-2 small text-muted">
                                        <div class="row">
                                            <div class="col-6">أهداف: ${player.goals}</div>
                                            <div class="col-6">تمريرات: ${player.assists}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                playersHtml += `
                        </div>
                    </div>
                `;
            }
        });

        $('#playersContainer').html(playersHtml);
        addCardEffects();
    }

    // ملء الفلاتر
    function populateFilters(players) {
        const teams = [...new Set(players.map(p => ({ id: p.team_id, name: p.team })))];

        let teamsOptions = '<option value="">جميع الفرق</option>';
        teams.forEach(team => {
            teamsOptions += `<option value="${team.id}">${team.name}</option>`;
        });
        $('#teamFilter').html(teamsOptions);
    }

    // تأثيرات بصرية للبطاقات
    function addCardEffects() {
        $('.player-card').hover(
            function() {
                $(this).addClass('shadow-lg');
                $(this).css('transform', 'translateY(-3px)');
            },
            function() {
                if (!$(this).hasClass('selected')) {
                    $(this).removeClass('shadow-lg');
                    $(this).css('transform', 'translateY(0)');
                }
            }
        );
    }

    // Formation change handler
    $('#defCount, #midCount, #fwdCount').change(function() {
        updateFormationValidation();
        showFormationPreview();
    });

    // Player selection handler with animations
    $(document).on('change', '.player-checkbox', function() {
        const playerId = $(this).val();
        const playerPrice = parseFloat($(this).data('price'));
        const playerPosition = $(this).data('position');
        const playerCard = $(this).closest('.player-card');

        if ($(this).is(':checked')) {
            // التحقق من القيود
            if (selectedPlayers.length >= 15) {
                $(this).prop('checked', false);
                showAlert('لا يمكن اختيار أكثر من 15 لاعباً', 'warning');
                return;
            }

            if (totalValue + playerPrice > maxBudget) {
                $(this).prop('checked', false);
                showAlert('تجاوزت الحد الأقصى للميزانية (100 مليون)', 'danger');
                return;
            }

            // إضافة اللاعب
            selectedPlayers.push({
                id: playerId,
                price: playerPrice,
                position: playerPosition,
                name: playerCard.find('h6').text()
            });
            totalValue += playerPrice;

            // تأثيرات بصرية
            playerCard.addClass('selected');
            playerCard.animate({
                backgroundColor: 'rgba(56, 0, 60, 0.1)',
                borderColor: '#38003c'
            }, 300);

        } else {
            // إزالة اللاعب
            selectedPlayers = selectedPlayers.filter(p => p.id != playerId);
            totalValue -= playerPrice;

            playerCard.removeClass('selected');
            playerCard.animate({
                backgroundColor: 'white',
                borderColor: '#e0e0e0'
            }, 300);
        }

        updateDisplay();
        updateCaptainOptions();
        validateForm();
    });

    // Filters with live search
    $('#teamFilter, #positionFilter').change(filterPlayers);
    $('#nameFilter').on('input', debounce(filterPlayers, 300));

    function filterPlayers() {
        const teamFilter = $('#teamFilter').val();
        const positionFilter = $('#positionFilter').val();
        const nameFilter = $('#nameFilter').val().toLowerCase();

        $('.player-item').each(function() {
            const matchTeam = !teamFilter || $(this).data('team') == teamFilter;
            const matchPosition = !positionFilter || $(this).data('position') == positionFilter;
            const matchName = !nameFilter || $(this).data('name').includes(nameFilter);

            if (matchTeam && matchPosition && matchName) {
                $(this).fadeIn(200);
            } else {
                $(this).fadeOut(200);
            }
        });
    }

    function updateCaptainOptions() {
        const captainSelect = $('#captain');
        const viceCaptainSelect = $('#vice_captain');

        captainSelect.empty().append('<option value="">اختر الكابتن</option>');
        viceCaptainSelect.empty().append('<option value="">اختر نائب الكابتن</option>');

        selectedPlayers.forEach(player => {
            captainSelect.append(`<option value="${player.id}">${player.name}</option>`);
            viceCaptainSelect.append(`<option value="${player.id}">${player.name}</option>`);
        });
    }

    function validateForm() {
        const isValid = selectedPlayers.length === 15 &&
                        totalValue <= maxBudget &&
                        $('#name').val() &&
                        $('#manager_name').val();

        $('#submitBtn').prop('disabled', !isValid);

        if (isValid) {
            $('#submitBtn').removeClass('btn-secondary').addClass('btn-fpl-primary');
        } else {
            $('#submitBtn').removeClass('btn-fpl-primary').addClass('btn-secondary');
        }
    }

    function showFormationPreview() {
        const def = $('#defCount').val();
        const mid = $('#midCount').val();
        const fwd = $('#fwdCount').val();

        $('#formationPreview').html(`
            <div class="text-center">
                <span class="badge bg-primary me-2">1 حارس</span>
                <span class="badge bg-success me-2">${def} مدافع</span>
                <span class="badge bg-info me-2">${mid} وسط</span>
                <span class="badge bg-danger">${fwd} مهاجم</span>
            </div>
        `);
    }

    function showAlert(message, type) {
        const alertClass = type === 'danger' ? 'alert-fpl-danger' : 'alert-warning';
        const alert = $(`
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        $('.container').first().prepend(alert);
        setTimeout(() => alert.fadeOut(), 5000);
    }

    // Debounce function for search
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Initialize
    updateDisplay();
    addCardEffects();
    showFormationPreview();

    // Form validation on input
    $('#name, #manager_name').on('input', validateForm);

    // إضافة دالة إرسال النموذج للنطاق العام
    window.submitTeamForm = function(event) {
        event.preventDefault();

        if (!validateFormData()) {
            return false;
        }

        const formData = {
            name: $('#name').val(),
            manager_name: $('#manager_name').val(),
            players: selectedPlayers.map(p => p.id),
            captain: $('#captain').val(),
            vice_captain: $('#vice_captain').val(),
            formation: {
                defenders: $('#defCount').val(),
                midfielders: $('#midCount').val(),
                forwards: $('#fwdCount').val()
            }
        };

        // إظهار مؤشر التحميل
        $('#submitBtn').html('<div class="loading-spinner me-2"></div>جاري الإنشاء...').prop('disabled', true);

        $.ajax({
            url: '{{ route("api.team.create") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.success) {
                    showAlert(response.message, 'success');

                    // إعادة توجيه بعد 2 ثانية
                    setTimeout(() => {
                        window.location.href = '{{ route("dashboard") }}';
                    }, 2000);
                } else {
                    showAlert(response.message, 'danger');
                    resetSubmitButton();
                }
            },
            error: function(xhr) {
                let message = 'حدث خطأ أثناء إنشاء الفريق';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showAlert(message, 'danger');
                resetSubmitButton();
            }
        });

        return false;
    };

    function validateFormData() {
        if (selectedPlayers.length !== 15) {
            showAlert('يجب اختيار 15 لاعباً بالضبط', 'warning');
            return false;
        }

        if (totalValue > maxBudget) {
            showAlert('تجاوزت الحد الأقصى للميزانية', 'danger');
            return false;
        }

        if (!$('#name').val() || !$('#manager_name').val()) {
            showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return false;
        }

        if (!$('#captain').val() || !$('#vice_captain').val()) {
            showAlert('يرجى اختيار الكابتن ونائب الكابتن', 'warning');
            return false;
        }

        return true;
    }

    function resetSubmitButton() {
        $('#submitBtn').html('<i class="fas fa-save me-2"></i>إنشاء الفريق').prop('disabled', false);
    }
});

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-fpl-success' :
                      type === 'danger' ? 'alert-fpl-danger' : 'alert-warning';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('.container').first().prepend(alert);
    setTimeout(() => alert.fadeOut(), 5000);
}
</script>
@endpush
@endsection
