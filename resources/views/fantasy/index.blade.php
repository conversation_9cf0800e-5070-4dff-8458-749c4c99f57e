@extends('layouts.fpl')

@section('title', 'Fantasy Premier League - الصفحة الرئيسية')

@section('content')
<!-- Hero Section -->
<section class="fpl-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1>Fantasy Premier League</h1>
                <p>أنشئ فريقك الخيالي واحسب النقاط حسب القوانين المحددة</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    @auth
                        <a href="{{ route('fantasy.create') }}" class="btn btn-fpl-secondary btn-lg">
                            <i class="fas fa-users me-2"></i>
                            إدارة فريقي
                        </a>
                    @else
                        <a href="{{ route('register') }}" class="btn btn-fpl-secondary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>
                            ابدأ الآن
                        </a>
                        <a href="{{ route('login') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="fpl-stat-card">
                    <div class="fpl-stat-number">{{ $teams->count() }}</div>
                    <div class="fpl-stat-label">فريق</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="fpl-stat-card">
                    <div class="fpl-stat-number">{{ $teams->sum(function($team) { return $team->players->count(); }) }}</div>
                    <div class="fpl-stat-label">لاعب</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="fpl-stat-card">
                    <div class="fpl-stat-number">100</div>
                    <div class="fpl-stat-label">مليون ميزانية</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="fpl-stat-card">
                    <div class="fpl-stat-number">15</div>
                    <div class="fpl-stat-label">لاعب في الفريق</div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">

    <!-- Rules Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="fpl-card">
                <div class="fpl-card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        القوانين الداخلية للمسابقة
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary">1. الفرعة:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>سيتم إجراء قرعة، والفائز بالقرعة هو الذي يختار التشكيلة أولاً</li>
                                <li><i class="fas fa-check text-success me-2"></i>الخصم له الحق في اختيار 4 لاعبين فقط من نفس التشكيلة</li>
                                <li><i class="fas fa-check text-success me-2"></i>إذا اختار الخصم أكثر من 4 لاعبين، نخصم منه 5 نقاط</li>
                                <li><i class="fas fa-check text-success me-2"></i>هذا القانون ينطبق فقط في الجولة الأولى</li>
                            </ul>

                            <h5 class="text-primary mt-4">2. اختيار الفريق:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>لكل مشارك الحق في اختيار فريق واحد طيلة الشهر</li>
                                <li><i class="fas fa-check text-success me-2"></i>في بداية كل شهر، يمكن تغيير الفريق واختيار أي فريق ما عدا:</li>
                                <li class="ms-4">
                                    <i class="fas fa-circle text-muted me-2" style="font-size: 0.5rem;"></i>مانشستر سيتي<br>
                                    <i class="fas fa-circle text-muted me-2" style="font-size: 0.5rem;"></i>ليفربول<br>
                                    <i class="fas fa-circle text-muted me-2" style="font-size: 0.5rem;"></i>أرسنال<br>
                                    <i class="fas fa-circle text-muted me-2" style="font-size: 0.5rem;"></i>تشيلسي<br>
                                    <i class="fas fa-circle text-muted me-2" style="font-size: 0.5rem;"></i>مانشستر يونايتد
                                </li>
                                <li><i class="fas fa-check text-success me-2"></i>إذا ربح الفريق الذي اخترته: تحصل على +10 نقاط</li>
                                <li><i class="fas fa-times text-danger me-2"></i>إذا خسر الفريق: تخصم منك -10 نقاط</li>
                                <li><i class="fas fa-times text-danger me-2"></i>إذا تعادل الفريق: تخصم منك -5 نقاط</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary">3. قيمة اللاعبين في التشكيلة:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>أي لاعب في تشكيلتك يجب ألا تتجاوز قيمته 12 مليون</li>
                                <li><i class="fas fa-times text-danger me-2"></i>إذا كان سعر اللاعب أكثر من 12 مليون، فلن يتم احتساب نقاطه</li>
                            </ul>

                            <h5 class="text-primary mt-4">4. توقعات إضافية (Bonus):</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-plus text-success me-2"></i>توقع الفريق الذي سيفوز بـ الدوري الإنجليزي (باستثناء ليفربول): +50 نقطة</li>
                                <li><i class="fas fa-plus text-success me-2"></i>توقع الفريق الذي سيهبط إلى الدرجة الثانية: +50 نقطة</li>
                            </ul>

                            <div class="mt-4 p-3 bg-light rounded">
                                <h6 class="text-primary mb-2">
                                    <i class="fas fa-calculator me-2"></i>
                                    نظام حساب النقاط:
                                </h6>
                                <small class="text-muted">
                                    يتم حساب النقاط تلقائياً حسب أداء اللاعبين في المباريات الفعلية
                                    مع تطبيق جميع القوانين المذكورة أعلاه
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="fpl-card h-100">
                <div class="card-body text-center p-4">
                    <i class="fas fa-plus-circle fa-4x mb-4" style="color: var(--fpl-purple);"></i>
                    <h4 class="card-title fw-bold mb-3">إنشاء فريق جديد</h4>
                    <p class="card-text text-muted mb-4">
                        ابدأ بإنشاء فريقك الخيالي واختر أفضل اللاعبين
                    </p>
                    @auth
                        <a href="{{ route('fantasy.create') }}" class="btn btn-fpl-primary btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            إنشاء فريق
                        </a>
                    @else
                        <a href="{{ route('register') }}" class="btn btn-fpl-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>
                            سجل أولاً
                        </a>
                    @endauth
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="fpl-card h-100">
                <div class="card-body text-center p-4">
                    <i class="fas fa-chart-line fa-4x mb-4" style="color: var(--fpl-green);"></i>
                    <h4 class="card-title fw-bold mb-3">حاسبة النقاط</h4>
                    <p class="card-text text-muted mb-4">
                        احسب نقاط فريقك تلقائياً حسب القوانين
                    </p>
                    <button class="btn btn-fpl-secondary btn-lg" onclick="scrollToRules()">
                        <i class="fas fa-calculator me-2"></i>
                        عرض القوانين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Teams Overview -->
    @if($teams->count() > 0)
    <div class="row mt-5">
        <div class="col-12">
            <div class="fpl-card">
                <div class="fpl-card-header" style="background: var(--fpl-gradient-secondary); color: var(--fpl-dark);">
                    <h3 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        فرق الدوري الإنجليزي الممتاز
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($teams as $team)
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="card team-card h-100" style="border-left: 4px solid {{ $team->color_primary }};">
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-1">{{ $team->name }}</h6>
                                    <small class="text-muted">{{ $team->short_name }}</small>
                                    <div class="mt-2">
                                        <span class="badge bg-secondary">{{ $team->players_count ?? 0 }} لاعب</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
function scrollToRules() {
    document.querySelector('.card-header').scrollIntoView({ 
        behavior: 'smooth' 
    });
}
</script>
@endsection
