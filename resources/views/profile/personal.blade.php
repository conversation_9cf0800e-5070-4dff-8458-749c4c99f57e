@extends('layouts.app')

@section('title', __('الملف الشخصي'))

@section('content')
<div class="container py-5 mt-4">
    <div class="row">
        <div class="col-12 text-center">
            <h1 class="display-5 fw-bold text-primary mb-4">{{ __('الملف الشخصي') }}</h1>
        </div>
    </div>
    
    <!-- Points Calculator Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-md-10">
            <div class="card shadow-sm border-0 rounded-3 overflow-hidden">
                <div class="card-header bg-gradient bg-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        {{ __('حاسبة النقاط') }}
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form id="points-calculator-form">
                        <!-- Round Score -->
                        <div class="mb-4">
                            <label for="round_score" class="form-label fw-bold">{{ __('نقاط الجولة') }}</label>
                            <input type="number" class="form-control form-control-lg" id="round_score" name="round_score" min="0" required>
                            <div class="form-text">{{ __('أدخل مجموع نقاط فريقك في هذه الجولة') }}</div>
                        </div>
                        
                        <!-- Round Number -->
                        <div class="mb-4">
                            <label for="round_number" class="form-label fw-bold">{{ __('رقم الجولة') }}</label>
                            <select class="form-select form-select-lg" id="round_number" name="round_number" required>
                                @for ($i = 1; $i <= 38; $i++)
                                    <option value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                        </div>
                        
                        <!-- Match Status -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">{{ __('نتيجة المباراة') }}</label>
                            <div class="d-flex gap-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="match_status" id="match_status_win" value="win">
                                    <label class="form-check-label" for="match_status_win">
                                        {{ __('فوز') }}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="match_status" id="match_status_draw" value="draw">
                                    <label class="form-check-label" for="match_status_draw">
                                        {{ __('تعادل') }}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="match_status" id="match_status_loss" value="loss">
                                    <label class="form-check-label" for="match_status_loss">
                                        {{ __('خسارة') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Opponent Players (Round 1 only) -->
                        <div class="mb-4" id="opponent-players-section" style="display: none;">
                            <label class="form-label fw-bold">{{ __('تجاوز عدد اللاعبين (Opponent\'s Players)') }}</label>
                            <div class="form-text mb-2">{{ __('هل اختار خصمك أكثر من 4 لاعبين؟') }}</div>
                            <div class="d-flex gap-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="opponent_more_players" id="opponent_more_players_yes" value="yes">
                                    <label class="form-check-label" for="opponent_more_players_yes">
                                        {{ __('نعم') }}
                                    </label>
                                    <div class="form-text">{{ __('سيتم خصم -5 نقاط من مجموع نقاطك') }}</div>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="opponent_more_players" id="opponent_more_players_no" value="no">
                                    <label class="form-check-label" for="opponent_more_players_no">
                                        {{ __('لا') }}
                                    </label>
                                    <div class="form-text">{{ __('لا يتم خصم أي نقاط') }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Expensive Player -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">{{ __('هل لديك لاعب غالي الثمن؟') }}</label>
                            <div class="d-flex gap-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="has_expensive_player" id="has_expensive_player_yes" value="yes">
                                    <label class="form-check-label" for="has_expensive_player_yes">
                                        {{ __('نعم') }}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="has_expensive_player" id="has_expensive_player_no" value="no">
                                    <label class="form-check-label" for="has_expensive_player_no">
                                        {{ __('لا') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div id="expensive-player-details" class="card p-3 bg-light mt-3" style="display: none;">
                                <div class="mb-3">
                                    <label for="expensive_player_name" class="form-label">{{ __('اسم اللاعب') }}</label>
                                    <input type="text" class="form-control" id="expensive_player_name" name="expensive_player_name">
                                </div>
                                
                                <div>
                                    <label for="expensive_player_points" class="form-label">{{ __('نقاط اللاعب') }}</label>
                                    <input type="number" class="form-control" id="expensive_player_points" name="expensive_player_points" min="0">
                                    <div class="form-text">{{ __('أدخل عدد النقاط التي سجلها اللاعب') }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- League Winner (Round 38 only) -->
                        <div class="mb-4" id="league-winner-section" style="display: none;">
                            <label class="form-label fw-bold">{{ __('الفريق الفائز بالدوري') }}</label>
                            <div class="d-flex gap-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="team_won_league" id="team_won_league_yes" value="yes">
                                    <label class="form-check-label" for="team_won_league_yes">
                                        {{ __('نعم') }}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="team_won_league" id="team_won_league_no" value="no">
                                    <label class="form-check-label" for="team_won_league_no">
                                        {{ __('لا') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">{{ __('حساب النقاط') }}</button>
                        </div>
                    </form>
                    
                    <!-- Results Section -->
                    <div id="results-container" class="mt-4" style="display: none;"></div>
                    
                    <!-- Points Breakdown -->
                    <div id="points-breakdown" class="mt-4" style="display: none;">
                        <h4>{{ __('تفاصيل النقاط') }}</h4>
                        <ul id="breakdown-list" class="list-group"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for this page -->
<style>
    /* Gradient Backgrounds */
    .bg-gradient {
        background: linear-gradient(135deg, var(--fpl-purple) 0%, #4a0e4e 100%);
    }
    
    /* Background Patterns */
    .bg-pattern {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }
    
    .bg-pattern-dots {
        background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    }
    
    /* Avatar Styles */
    .avatar-circle {
        width: 80px;
        height: 80px;
        background-color: var(--fpl-purple);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        font-size: 2.5rem;
        font-weight: bold;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    
    /* User Stats */
    .stat-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        font-size: 1.2rem;
    }
    
    .stat-value {
        font-size: 1.2rem;
        font-weight: 600;
    }
    
    /* User Info */
    .info-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Team Logo */
    .team-logo-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }
    
    /* Stats Cards */
    .stats-card {
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .stats-icon-new {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }
    
    .stats-number {
        font-size: 1.8rem;
        font-weight: 700;
    }
    
    /* Action Cards */
    .action-card {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        position: relative;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .action-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        margin-right: 1rem;
    }
    
    .action-content {
        flex: 1;
    }
    
    .action-arrow {
        opacity: 0;
        transition: all 0.3s ease;
    }
    
    .action-card:hover .action-arrow {
        opacity: 1;
        transform: translateX(-5px);
    }
    
    /* Hover Effects */
    .hover-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* Connect Icon */
    .connect-icon-container {
        width: 80px;
        height: 80px;
        background-color: #f8f9fa;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }
    
    /* Rules Styling */
    .rules-container {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }
    
    .rule-section {
        padding: 1rem 1.5rem;
        border-radius: 10px;
        background-color: rgba(255, 255, 255, 0.7);
        margin-bottom: 1.5rem;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    }
    
    .rule-title {
        font-weight: 700;
        color: var(--fpl-purple);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }
    
    .rule-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        background-color: var(--fpl-purple);
        color: white;
        border-radius: 50%;
        margin-right: 0.75rem;
        font-size: 1rem;
    }
    
    .rule-list {
        padding-right: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .rule-list li {
        margin-bottom: 0.5rem;
        position: relative;
    }
    
    .rule-list li:last-child {
        margin-bottom: 0;
    }
    
    .restricted-teams {
        background-color: rgba(220, 53, 69, 0.1);
        border-radius: 8px;
        padding: 1rem 2.5rem 1rem 1rem;
        margin: 0.5rem 1.5rem 1rem 0;
    }
    
    .restricted-teams li {
        color: #dc3545;
        font-weight: 500;
    }
    
    .rules-logo {
        filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.1));
    }
</style>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('points-calculator-form');
        const roundNumberSelect = document.getElementById('round_number');
        const opponentPlayersSection = document.getElementById('opponent-players-section');
        const leagueWinnerSection = document.getElementById('league-winner-section');
        const hasExpensivePlayerYes = document.getElementById('has_expensive_player_yes');
        const hasExpensivePlayerNo = document.getElementById('has_expensive_player_no');
        const expensivePlayerDetails = document.getElementById('expensive-player-details');

        const resultsContainer = document.getElementById('results-container');
        const pointsBreakdown = document.getElementById('points-breakdown');
        const breakdownList = document.getElementById('breakdown-list');
        
        // Show/hide sections based on round number
        roundNumberSelect.addEventListener('change', function() {
            const roundNumber = parseInt(this.value);
            
            // Show opponent players section only for round 1
            opponentPlayersSection.style.display = roundNumber === 1 ? 'block' : 'none';
            
            // Show league winner section only for round 38
            leagueWinnerSection.style.display = roundNumber === 38 ? 'block' : 'none';
        });
        
        // Show/hide expensive player details
        hasExpensivePlayerYes.addEventListener('change', function() {
            if (this.checked) {
                expensivePlayerDetails.style.display = 'block';
            }
        });
        
        hasExpensivePlayerNo.addEventListener('change', function() {
            if (this.checked) {
                expensivePlayerDetails.style.display = 'none';
            }
        });
        
        // Form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const roundScore = parseInt(document.getElementById('round_score').value) || 0;
            const roundNumber = parseInt(document.getElementById('round_number').value) || 1;
            const matchStatus = document.querySelector('input[name="match_status"]:checked')?.value;
            
            // Calculate points
            let totalPoints = roundScore;
            let breakdown = [];
            
            // Add round score to breakdown
            breakdown.push({
                label: '{{ __('app.round_score') }}',
                points: roundScore,
                type: 'neutral'
            });
            
            // Add/subtract points based on match status
            if (matchStatus) {
                let matchPoints = 0;
                let matchLabel = '';
                let matchType = '';
                
                if (matchStatus === 'win') {
                    matchPoints = 10;
                    matchLabel = '{{ __('app.win_bonus') }}';
                    matchType = 'positive';
                } else if (matchStatus === 'draw') {
                    matchPoints = -5;
                    matchLabel = '{{ __('app.draw_penalty') }}';
                    matchType = 'negative';
                } else if (matchStatus === 'loss') {
                    matchPoints = -10;
                    matchLabel = '{{ __('app.loss_penalty') }}';
                    matchType = 'negative';
                }
                
                totalPoints += matchPoints;
                
                breakdown.push({
                    label: matchLabel,
                    points: matchPoints,
                    type: matchType
                });
            }
            
            // Check opponent players (round 1 only)
            if (roundNumber === 1) {
                const opponentMorePlayers = document.querySelector('input[name="opponent_more_players"]:checked')?.value;
                
                if (opponentMorePlayers === 'yes') {
                    totalPoints -= 5;
                    
                    breakdown.push({
                        label: '{{ __('تجاوز عدد اللاعبين - خصم') }}',
                        points: -5,
                        type: 'negative'
                    });
                }
            }
            
            // Check expensive player
            const hasExpensivePlayer = document.querySelector('input[name="has_expensive_player"]:checked')?.value;
            
            if (hasExpensivePlayer === 'yes') {
                const playerName = document.getElementById('expensive_player_name').value;
                const playerPoints = parseInt(document.getElementById('expensive_player_points').value) || 0;
                
                if (playerPoints > 0) {
                    totalPoints -= playerPoints;
                    
                    breakdown.push({
                        label: '{{ __('app.expensive_player_penalty') }}: ' + playerName,
                        points: -playerPoints,
                        type: 'negative'
                    });
                }
            }
            
            // Check league winner (round 38 only)
            if (roundNumber === 38) {
                const teamWonLeague = document.querySelector('input[name="team_won_league"]:checked')?.value;
                
                if (teamWonLeague === 'yes') {
                    totalPoints += 15;
                    
                    breakdown.push({
                        label: '{{ __('app.league_winner_bonus') }}',
                        points: 15,
                        type: 'positive'
                    });
                } else if (teamWonLeague === 'no') {
                    totalPoints -= 5;
                    
                    breakdown.push({
                        label: '{{ __('app.league_loser_penalty') }}',
                        points: -5,
                        type: 'negative'
                    });
                }
            }
            
            // Display results
            resultsContainer.innerHTML = `
                <div class="text-center p-4 bg-light rounded">
                    <h2 class="display-4 fw-bold ${totalPoints >= 0 ? 'text-success' : 'text-danger'}">\${totalPoints}</h2>
                    <p class="lead">{{ __('app.total_points') }}</p>
                </div>
            `;
            
            // Display breakdown
            breakdownList.innerHTML = '';
            breakdown.forEach(item => {
                const sign = item.points >= 0 ? '+' : '';
                breakdownList.innerHTML += `
                    <li class="list-group-item d-flex justify-content-between align-items-center ${item.type === 'positive' ? 'list-group-item-success' : item.type === 'negative' ? 'list-group-item-danger' : ''}">
                        ${item.label}
                        <span class="badge bg-${item.type === 'positive' ? 'success' : item.type === 'negative' ? 'danger' : 'secondary'} rounded-pill">${sign}${item.points}</span>
                    </li>
                `;
            });
            
            // Show breakdown
            pointsBreakdown.style.display = 'block';
            resultsContainer.style.display = 'block';
        });
        
        // Initialize sections based on default round number
        const initialRoundNumber = parseInt(roundNumberSelect.value) || 1;
        opponentPlayersSection.style.display = initialRoundNumber === 1 ? 'block' : 'none';
        leagueWinnerSection.style.display = initialRoundNumber === 38 ? 'block' : 'none';
    });
</script>
@endsection