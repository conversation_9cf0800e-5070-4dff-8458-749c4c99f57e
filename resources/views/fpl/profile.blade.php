@extends('layouts.app')

@section('title', __('auth.profile_title'))

@section('content')
<div class="container py-5">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient text-white shadow-lg">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-user-circle me-2"></i>
                                {{ $user->fpl_team_name }}
                            </h2>
                            <p class="mb-1 opacity-75">
                                <i class="fas fa-user me-2"></i>
                                {{ __('auth.manager_name') }}: {{ $user->fpl_manager_name }}
                            </p>
                            <p class="mb-0 opacity-75">
                                <i class="fas fa-clock me-2"></i>
                                {{ __('app.last_updated') }}: {{ $user->fpl_last_updated ? $user->fpl_last_updated->format('d/m/Y H:i') : __('app.not_specified') }}
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <button id="updateBtn" class="btn btn-light btn-lg">
                                <i class="fas fa-sync-alt me-2"></i>
                                تحديث البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-trophy text-warning"></i>
                    </div>
                    <h3 class="stats-number text-primary" id="totalPoints">{{ number_format($user->fpl_total_points) }}</h3>
                    <p class="stats-label text-muted mb-0">إجمالي النقاط</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-chart-line text-success"></i>
                    </div>
                    <h3 class="stats-number text-success" id="overallRank">{{ $user->getFormattedOverallRank() }}</h3>
                    <p class="stats-label text-muted mb-0">الترتيب العام</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-star text-info"></i>
                    </div>
                    <h3 class="stats-number text-info" id="gameweekPoints">{{ $user->fpl_gameweek_points }}</h3>
                    <p class="stats-label text-muted mb-0">نقاط الجولة الحالية</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-coins text-warning"></i>
                    </div>
                    <h3 class="stats-number text-warning" id="teamValue">{{ $user->getFormattedTeamValue() }}</h3>
                    <p class="stats-label text-muted mb-0">قيمة الفريق</p>
                    <small class="text-muted">البنك: <span id="bankValue">{{ $user->getFormattedBank() }}</span></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Gameweek Info -->
    @if($currentGameweek)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        الجولة الحالية - {{ $currentGameweek['name'] }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>تاريخ البداية:</strong> {{ \Carbon\Carbon::parse($currentGameweek['deadline_time'])->format('d/m/Y H:i') }}</p>
                            <p><strong>الحالة:</strong> 
                                @if($currentGameweek['is_current'])
                                    <span class="badge bg-success">جارية</span>
                                @elseif($currentGameweek['finished'])
                                    <span class="badge bg-secondary">منتهية</span>
                                @else
                                    <span class="badge bg-warning">قادمة</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>أعلى نقاط:</strong> {{ $currentGameweek['highest_score'] ?? 'غير محدد' }}</p>
                            <p><strong>متوسط النقاط:</strong> {{ $currentGameweek['average_entry_score'] ?? 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Team Details Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        تشكيلة الفريق
                    </h5>
                    <button id="loadTeamBtn" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>
                        عرض التشكيلة
                    </button>
                </div>
                <div class="card-body">
                    <div id="teamDetails" class="d-none">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                    <div id="teamPlaceholder" class="text-center text-muted py-4">
                        <i class="fas fa-users fa-3x mb-3 opacity-50"></i>
                        <p>اضغط على "عرض التشكيلة" لرؤية لاعبي فريقك</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <a href="https://fantasy.premierleague.com" target="_blank" class="btn btn-outline-primary w-100">
                                <i class="fas fa-external-link-alt me-2"></i>
                                فتح FPL الرسمي
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="{{ route('fpl.connect') }}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات الحساب
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button id="refreshAllBtn" class="btn btn-outline-success w-100">
                                <i class="fas fa-refresh me-2"></i>
                                تحديث شامل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-gradient {
    background: linear-gradient(135deg, var(--fpl-purple) 0%, var(--fpl-green) 100%);
}

.stats-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.stats-icon i {
    font-size: 2.5rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.stats-label {
    font-size: 0.9rem;
    font-weight: 500;
}

.card {
    border-radius: 15px;
}

.btn {
    border-radius: 10px;
}

.team-player {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.team-player:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.captain-badge {
    background: #ffd700;
    color: #000;
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 15px;
    font-weight: bold;
}

.vice-captain-badge {
    background: #c0c0c0;
    color: #000;
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 15px;
    font-weight: bold;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update button functionality
    document.getElementById('updateBtn').addEventListener('click', function() {
        updateFPLData();
    });

    // Load team button functionality
    document.getElementById('loadTeamBtn').addEventListener('click', function() {
        loadTeamDetails();
    });

    // Refresh all button functionality
    document.getElementById('refreshAllBtn').addEventListener('click', function() {
        updateFPLData();
        setTimeout(() => {
            loadTeamDetails();
        }, 2000);
    });
});

function updateFPLData() {
    const btn = document.getElementById('updateBtn');
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    btn.disabled = true;

    fetch('{{ route("fpl.update") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the displayed data
            document.getElementById('totalPoints').textContent = data.data.total_points;
            document.getElementById('overallRank').textContent = data.data.overall_rank;
            document.getElementById('gameweekPoints').textContent = data.data.gameweek_points;
            document.getElementById('teamValue').textContent = data.data.team_value;
            document.getElementById('bankValue').textContent = data.data.bank;
            
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function loadTeamDetails() {
    const btn = document.getElementById('loadTeamBtn');
    const teamDetails = document.getElementById('teamDetails');
    const teamPlaceholder = document.getElementById('teamPlaceholder');
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
    btn.disabled = true;
    
    teamPlaceholder.classList.add('d-none');
    teamDetails.classList.remove('d-none');

    fetch('{{ route("fpl.team-details") }}')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayTeamDetails(data.data);
        } else {
            showAlert('danger', data.message);
            teamDetails.classList.add('d-none');
            teamPlaceholder.classList.remove('d-none');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في تحميل بيانات الفريق');
        teamDetails.classList.add('d-none');
        teamPlaceholder.classList.remove('d-none');
    })
    .finally(() => {
        btn.innerHTML = '<i class="fas fa-eye me-2"></i>عرض التشكيلة';
        btn.disabled = false;
    });
}

function displayTeamDetails(teamData) {
    const teamDetails = document.getElementById('teamDetails');
    
    let html = '<div class="row">';
    
    teamData.picks.forEach((player, index) => {
        const isPlaying = index < 11;
        const badges = [];
        
        if (player.is_captain) badges.push('<span class="captain-badge">كابتن</span>');
        if (player.is_vice_captain) badges.push('<span class="vice-captain-badge">نائب الكابتن</span>');
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="team-player ${!isPlaying ? 'opacity-75' : ''}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${player.name} <small class="text-muted">(${player.team})</small></h6>
                            <small class="text-muted">${player.position} - £${player.price}m</small>
                            ${badges.length > 0 ? '<div class="mt-1">' + badges.join(' ') + '</div>' : ''}
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-primary">${player.points} نقطة</div>
                            <small class="text-muted">${isPlaying ? 'أساسي' : 'احتياط'}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    
    if (teamData.active_chip) {
        html += `
            <div class="alert alert-info mt-3">
                <i class="fas fa-magic me-2"></i>
                الشريحة المفعلة: <strong>${teamData.active_chip}</strong>
            </div>
        `;
    }
    
    teamDetails.innerHTML = html;
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
@endsection
