@extends('layouts.calculator')

@section('title', __('app.dashboard'))

@section('content')
<div class="container py-4">
    <!-- User Info Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="fpl-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">
                                <i class="fas fa-user-circle me-2 text-primary"></i>
                                {{ $userData['player_first_name'] }} {{ $userData['player_last_name'] }}
                            </h4>
                            <p class="text-muted mb-0">
                                <i class="fas fa-id-badge me-1"></i>
                                {{ __('app.fpl_team_id') }}: {{ $userData['id'] }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-calendar me-1"></i>
                                {{ __('app.current_gameweek') }}: {{ $currentGameweek }}
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ route('fpl.disconnect') }}" class="btn btn-outline-danger">
                                <i class="fas fa-unlink me-2"></i>
                                {{ __('app.disconnect_account') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Points Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="fpl-card text-center">
                <div class="card-body">
                    <div class="stat-icon mb-2">
                        <i class="fas fa-trophy fa-2x text-warning"></i>
                    </div>
                    <h3 class="fw-bold text-primary">{{ number_format($userData['summary_overall_points']) }}</h3>
                    <p class="text-muted mb-0">{{ __('app.official_points') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="fpl-card text-center">
                <div class="card-body">
                    <div class="stat-icon mb-2">
                        <i class="fas fa-calculator fa-2x text-success"></i>
                    </div>
                    <h3 class="fw-bold text-success">{{ $totalCustomPoints }}</h3>
                    <p class="text-muted mb-0">{{ __('app.custom_points_total') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="fpl-card text-center">
                <div class="card-body">
                    <div class="stat-icon mb-2">
                        @if($totalCustomPoints - $totalOfficialPoints > 0)
                            <i class="fas fa-arrow-up fa-2x text-success"></i>
                        @elseif($totalCustomPoints - $totalOfficialPoints < 0)
                            <i class="fas fa-arrow-down fa-2x text-danger"></i>
                        @else
                            <i class="fas fa-equals fa-2x text-info"></i>
                        @endif
                    </div>
                    <h3 class="fw-bold {{ $totalCustomPoints - $totalOfficialPoints > 0 ? 'text-success' : ($totalCustomPoints - $totalOfficialPoints < 0 ? 'text-danger' : 'text-info') }}">
                        {{ $totalCustomPoints - $totalOfficialPoints > 0 ? '+' : '' }}{{ $totalCustomPoints - $totalOfficialPoints }}
                    </h3>
                    <p class="text-muted mb-0">{{ __('app.points_difference') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="fpl-card text-center">
                <div class="card-body">
                    <div class="stat-icon mb-2">
                        <i class="fas fa-ranking-star fa-2x text-info"></i>
                    </div>
                    <h3 class="fw-bold text-info">{{ number_format($userData['summary_overall_rank']) }}</h3>
                    <p class="text-muted mb-0">{{ __('app.overall_rank') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Team Players -->
    <div class="row">
        <div class="col-12">
            <div class="fpl-card">
                <div class="fpl-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        {{ __('app.my_team') }} - {{ __('app.current_gameweek') }} {{ $currentGameweek }}
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ __('app.player_name') }}</th>
                                    <th>{{ __('app.player_position') }}</th>
                                    <th>{{ __('app.goals') }}</th>
                                    <th>{{ __('app.assists') }}</th>
                                    <th>{{ __('app.minutes_played') }}</th>
                                    <th>{{ __('app.official_points') }}</th>
                                    <th>{{ __('app.custom_points') }}</th>
                                    <th>{{ __('app.points_difference') }}</th>
                                    <th>{{ __('app.multiplier') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($selectedPlayers as $playerData)
                                    <tr class="{{ $playerData['is_captain'] ? 'table-warning' : ($playerData['is_vice_captain'] ? 'table-info' : '') }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <h6 class="mb-0">{{ $playerData['player']['name'] }}</h6>
                                                    <small class="text-muted">{{ $playerData['player']['team_short'] }}</small>
                                                    @if($playerData['is_captain'])
                                                        <span class="badge bg-warning text-dark ms-1">C</span>
                                                    @elseif($playerData['is_vice_captain'])
                                                        <span class="badge bg-info ms-1">VC</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $playerData['player']['position'] }}</span>
                                        </td>
                                        <td>{{ $playerData['player']['goals_scored'] }}</td>
                                        <td>{{ $playerData['player']['assists'] }}</td>
                                        <td>{{ $playerData['player']['minutes'] }}</td>
                                        <td>
                                            <span class="fw-bold text-primary">{{ $playerData['official_points'] }}</span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">{{ $playerData['custom_points'] }}</span>
                                        </td>
                                        <td>
                                            @php
                                                $diff = $playerData['custom_points'] - $playerData['official_points'];
                                            @endphp
                                            <span class="fw-bold {{ $diff > 0 ? 'text-success' : ($diff < 0 ? 'text-danger' : 'text-muted') }}">
                                                {{ $diff > 0 ? '+' : '' }}{{ $diff }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $playerData['multiplier'] }}x</span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <i class="fas fa-exclamation-triangle fa-2x text-muted mb-2"></i>
                                            <br>{{ __('app.no_team_data') }}
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="fpl-card">
                <div class="fpl-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        {{ __('app.quick_actions') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('fpl.players') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-2"></i>
                                {{ __('app.all_players') }}
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success w-100" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-2"></i>
                                {{ __('app.refresh_data') }}
                            </button>
                        </div>

                        <div class="col-md-3">
                            <button class="btn btn-outline-warning w-100" onclick="exportData()">
                                <i class="fas fa-download me-2"></i>
                                {{ __('app.export_data') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function refreshData() {
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<div class="loading-spinner me-2"></div>{{ __("app.refreshing") }}...';
    btn.disabled = true;
    
    // Reload the page to get fresh data
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

function exportData() {
    // Prepare data for export
    const teamData = {
        user: {
            name: '{{ $userData["player_first_name"] }} {{ $userData["player_last_name"] }}',
            id: '{{ $userData["id"] }}',
            official_points: {{ $userData['summary_overall_points'] }},
            custom_points: {{ $totalCustomPoints }},
            rank: {{ $userData['summary_overall_rank'] }}
        },
        players: [
            @foreach($selectedPlayers as $playerData)
            {
                name: '{{ $playerData["player"]["name"] }}',
                position: '{{ $playerData["player"]["position"] }}',
                team: '{{ $playerData["player"]["team_short"] }}',
                official_points: {{ $playerData['official_points'] }},
                custom_points: {{ $playerData['custom_points'] }},
                is_captain: {{ $playerData['is_captain'] ? 'true' : 'false' }},
                multiplier: {{ $playerData['multiplier'] }}
            },
            @endforeach
        ]
    };
    
    // Create and download JSON file
    const dataStr = JSON.stringify(teamData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'fpl_custom_points_gw{{ $currentGameweek }}.json';
    link.click();
    URL.revokeObjectURL(url);
    
    showAlert('{{ __("app.data_exported") }}', 'success');
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-info';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-info-circle';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('.container').first().prepend(alert);
    setTimeout(() => alert.fadeOut(), 5000);
}

// Auto-refresh every 5 minutes
setInterval(() => {
    if (document.visibilityState === 'visible') {
        window.location.reload();
    }
}, 300000);
</script>
@endpush
@endsection
