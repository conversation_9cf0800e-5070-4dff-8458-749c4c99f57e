@extends('layouts.calculator')

@section('title', 'جميع اللاعبين - مقارنة النقاط')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 fw-bold text-white mb-2">
                        <i class="fas fa-users me-3"></i>
                        {{ __('app.all_players') }}
                    </h1>
                    <p class="text-white-50 mb-0">{{ __('app.compare_official_custom') }}</p>
                </div>
                <div>
                    <a href="{{ route('fpl.dashboard') }}" class="btn btn-fpl-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        {{ __('app.back_to_dashboard') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="fpl-card">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="teamFilter" class="form-label fw-bold">{{ __('app.team') }}</label>
                            <select class="form-select" id="teamFilter">
                                <option value="">{{ __('app.all_teams') }}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="positionFilter" class="form-label fw-bold">{{ __('app.position') }}</label>
                            <select class="form-select" id="positionFilter">
                                <option value="">{{ __('app.all_positions') }}</option>
                                <option value="GKP">{{ __('app.goalkeeper') }}</option>
                                <option value="DEF">{{ __('app.defender') }}</option>
                                <option value="MID">{{ __('app.midfielder') }}</option>
                                <option value="FWD">{{ __('app.forward') }}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchPlayer" class="form-label fw-bold">{{ __('app.search') }}</label>
                            <input type="text" class="form-control" id="searchPlayer" placeholder="{{ __('app.search_players') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold">&nbsp;</label>
                            <button class="btn btn-fpl-primary w-100" onclick="refreshPlayers()">
                                <i class="fas fa-sync-alt me-2"></i>
                                {{ __('app.refresh') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="fpl-card text-center">
                <div class="card-body">
                    <h4 class="fw-bold text-primary" id="totalPlayers">-</h4>
                    <p class="text-muted mb-0">{{ __('app.total_players') }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="fpl-card text-center">
                <div class="card-body">
                    <h4 class="fw-bold text-success" id="avgCustomPoints">-</h4>
                    <p class="text-muted mb-0">{{ __('app.avg_custom_points') }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="fpl-card text-center">
                <div class="card-body">
                    <h4 class="fw-bold text-info" id="avgOfficialPoints">-</h4>
                    <p class="text-muted mb-0">{{ __('app.avg_official_points') }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="fpl-card text-center">
                <div class="card-body">
                    <h4 class="fw-bold text-warning" id="avgDifference">-</h4>
                    <p class="text-muted mb-0">{{ __('app.avg_difference') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Players Table -->
    <div class="row">
        <div class="col-12">
            <div class="fpl-card">
                <div class="fpl-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            {{ __('app.players_comparison') }}
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-light" onclick="sortBy('custom_points')">
                                {{ __('app.sort_by_custom') }}
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light" onclick="sortBy('official_points')">
                                {{ __('app.sort_by_official') }}
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light" onclick="sortBy('difference')">
                                {{ __('app.sort_by_difference') }}
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="playersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ __('app.player_name') }}</th>
                                    <th>{{ __('app.team') }}</th>
                                    <th>{{ __('app.position') }}</th>
                                    <th>{{ __('app.price') }}</th>
                                    <th>{{ __('app.official_points') }}</th>
                                    <th>{{ __('app.custom_points') }}</th>
                                    <th>{{ __('app.difference') }}</th>
                                    <th>{{ __('app.selected_by') }}</th>
                                    <th>{{ __('app.form') }}</th>
                                </tr>
                            </thead>
                            <tbody id="playersTableBody">
                                <tr>
                                    <td colspan="9" class="text-center py-5">
                                        <div class="loading-spinner mb-3"></div>
                                        <h5>{{ __('app.loading_players') }}...</h5>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let allPlayers = [];
let filteredPlayers = [];
let currentSort = 'custom_points';
let sortDirection = 'desc';

$(document).ready(function() {
    loadPlayers();
    loadTeamsFilter();
    
    // Filter events
    $('#teamFilter, #positionFilter').change(function() {
        filterPlayers();
    });
    
    $('#searchPlayer').on('input', debounce(function() {
        filterPlayers();
    }, 500));
});

function loadPlayers() {
    $.ajax({
        url: '{{ route("api.fpl.players") }}',
        method: 'GET',
        success: function(players) {
            allPlayers = players;
            filteredPlayers = players;
            displayPlayers(players);
            updateStats(players);
        },
        error: function() {
            $('#playersTableBody').html(`
                <tr>
                    <td colspan="9" class="text-center py-4 text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <br>{{ __('app.error_loading_data') }}
                    </td>
                </tr>
            `);
        }
    });
}

function loadTeamsFilter() {
    $.ajax({
        url: '{{ route("api.fpl.teams") }}',
        method: 'GET',
        success: function(teams) {
            let options = '<option value="">{{ __("app.all_teams") }}</option>';
            teams.forEach(function(team) {
                options += `<option value="${team.short_name}">${team.name}</option>`;
            });
            $('#teamFilter').html(options);
        }
    });
}

function filterPlayers() {
    const teamFilter = $('#teamFilter').val();
    const positionFilter = $('#positionFilter').val();
    const searchFilter = $('#searchPlayer').val().toLowerCase();
    
    filteredPlayers = allPlayers.filter(player => {
        const matchesTeam = !teamFilter || player.team_short === teamFilter;
        const matchesPosition = !positionFilter || player.position === positionFilter;
        const matchesSearch = !searchFilter || 
            player.name.toLowerCase().includes(searchFilter) ||
            player.full_name.toLowerCase().includes(searchFilter);
        
        return matchesTeam && matchesPosition && matchesSearch;
    });
    
    displayPlayers(filteredPlayers);
    updateStats(filteredPlayers);
}

function displayPlayers(players) {
    if (players.length === 0) {
        $('#playersTableBody').html(`
            <tr>
                <td colspan="9" class="text-center py-4">
                    <i class="fas fa-search fa-2x mb-2 text-muted"></i>
                    <br>{{ __('app.no_players_found') }}
                </td>
            </tr>
        `);
        return;
    }
    
    let tableHtml = '';
    players.forEach(function(player) {
        const difference = player.custom_points - player.total_points;
        const diffClass = difference > 0 ? 'text-success' : (difference < 0 ? 'text-danger' : 'text-muted');
        
        tableHtml += `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <h6 class="mb-0">${player.name}</h6>
                            <small class="text-muted">${player.full_name}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-secondary">${player.team_short}</span>
                </td>
                <td>
                    <span class="badge bg-primary">${player.position}</span>
                </td>
                <td class="fw-bold">£${player.price}m</td>
                <td>
                    <span class="fw-bold text-primary">${player.total_points}</span>
                </td>
                <td>
                    <span class="fw-bold text-success">${player.custom_points}</span>
                </td>
                <td>
                    <span class="fw-bold ${diffClass}">
                        ${difference > 0 ? '+' : ''}${difference}
                    </span>
                </td>
                <td>${player.selected_by_percent}%</td>
                <td>
                    <span class="badge bg-info">${player.form}</span>
                </td>
            </tr>
        `;
    });
    
    $('#playersTableBody').html(tableHtml);
}

function updateStats(players) {
    if (players.length === 0) {
        $('#totalPlayers').text('0');
        $('#avgCustomPoints').text('-');
        $('#avgOfficialPoints').text('-');
        $('#avgDifference').text('-');
        return;
    }
    
    const totalCustom = players.reduce((sum, p) => sum + p.custom_points, 0);
    const totalOfficial = players.reduce((sum, p) => sum + p.total_points, 0);
    const avgCustom = (totalCustom / players.length).toFixed(1);
    const avgOfficial = (totalOfficial / players.length).toFixed(1);
    const avgDiff = (avgCustom - avgOfficial).toFixed(1);
    
    $('#totalPlayers').text(players.length);
    $('#avgCustomPoints').text(avgCustom);
    $('#avgOfficialPoints').text(avgOfficial);
    $('#avgDifference').text(avgDiff > 0 ? '+' + avgDiff : avgDiff);
}

function sortBy(field) {
    if (currentSort === field) {
        sortDirection = sortDirection === 'desc' ? 'asc' : 'desc';
    } else {
        currentSort = field;
        sortDirection = 'desc';
    }
    
    filteredPlayers.sort((a, b) => {
        let aVal, bVal;
        
        switch (field) {
            case 'custom_points':
                aVal = a.custom_points;
                bVal = b.custom_points;
                break;
            case 'official_points':
                aVal = a.total_points;
                bVal = b.total_points;
                break;
            case 'difference':
                aVal = a.custom_points - a.total_points;
                bVal = b.custom_points - b.total_points;
                break;
            default:
                return 0;
        }
        
        if (sortDirection === 'desc') {
            return bVal - aVal;
        } else {
            return aVal - bVal;
        }
    });
    
    displayPlayers(filteredPlayers);
}

function refreshPlayers() {
    loadPlayers();
    showAlert('{{ __("app.data_refreshed") }}', 'success');
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-info';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-info-circle';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('.container').first().prepend(alert);
    setTimeout(() => alert.fadeOut(), 5000);
}
</script>
@endpush
@endsection
