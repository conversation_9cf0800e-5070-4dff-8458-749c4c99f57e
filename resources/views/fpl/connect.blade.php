@extends('layouts.app')

@section('title', 'ربط حساب Fantasy Premier League')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Hero Section -->
            <div class="text-center mb-5">
                <div class="fpl-logo mb-4">
                    <i class="fas fa-link fa-4x" style="color: var(--fpl-green);"></i>
                </div>
                <h1 class="display-5 fw-bold text-white mb-3">
                    {{ __('app.connect_fpl_account') }}
                </h1>
                <p class="lead text-white-50">
                    {{ __('app.connect_fpl_description') }}
                </p>
            </div>

            <!-- Connection Form -->
            <div class="fpl-card">
                <div class="fpl-card-header text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        {{ __('app.enter_fpl_id') }}
                    </h4>
                </div>
                <div class="card-body p-4">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ $errors->first() }}
                        </div>
                    @endif

                    @if (session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                        </div>
                    @endif

                    <form action="{{ route('fpl.store') }}" method="POST" id="connectForm">
                        @csrf
                        
                        <div class="mb-4">
                            <label for="manager_id" class="form-label fw-bold">
                                <i class="fas fa-id-card me-2"></i>
                                رقم المدير (Manager ID)
                                <span class="text-danger">*</span>
                            </label>
                            <input
                                type="number"
                                class="form-control form-control-lg @error('manager_id') is-invalid @enderror"
                                id="manager_id"
                                name="manager_id"
                                placeholder="مثال: 123456"
                                value="{{ old('manager_id') }}"
                                required
                                min="1"
                            >

                            @error('manager_id')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror

                            <div class="form-text">
                                <i class="fas fa-shield-alt me-1"></i>
                                بياناتك آمنة ولن نصل إلى معلوماتك الشخصية
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-fpl-success btn-lg" id="connectBtn">
                                <i class="fas fa-link me-2"></i>
                                {{ __('app.connect_account') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- How to Find Your ID -->
            <div class="fpl-card mt-4">
                <div class="fpl-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        {{ __('app.how_to_find_id') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="step-card">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h6>{{ __('app.step_1_title') }}</h6>
                                    <p class="text-muted mb-0">{{ __('app.step_1_desc') }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="step-card">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h6>{{ __('app.step_2_title') }}</h6>
                                    <p class="text-muted mb-0">{{ __('app.step_2_desc') }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="step-card">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h6>{{ __('app.step_3_title') }}</h6>
                                    <p class="text-muted mb-0">{{ __('app.step_3_desc') }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="step-card">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h6>{{ __('app.step_4_title') }}</h6>
                                    <p class="text-muted mb-0">{{ __('app.step_4_desc') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>{{ __('app.example') }}:</strong>
                        {{ __('app.id_example') }}
                    </div>
                </div>
            </div>

            <!-- Features Preview -->
            <div class="fpl-card mt-4">
                <div class="fpl-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        {{ __('app.what_you_get') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-calculator text-success me-3"></i>
                                <div>
                                    <h6 class="mb-1">{{ __('app.custom_points') }}</h6>
                                    <p class="text-muted mb-0">{{ __('app.custom_points_desc') }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-chart-line text-info me-3"></i>
                                <div>
                                    <h6 class="mb-1">{{ __('app.real_time_data') }}</h6>
                                    <p class="text-muted mb-0">{{ __('app.real_time_desc') }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-balance-scale text-warning me-3"></i>
                                <div>
                                    <h6 class="mb-1">{{ __('app.comparison') }}</h6>
                                    <p class="text-muted mb-0">{{ __('app.comparison_desc') }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-users text-primary me-3"></i>
                                <div>
                                    <h6 class="mb-1">{{ __('app.team_analysis') }}</h6>
                                    <p class="text-muted mb-0">{{ __('app.team_analysis_desc') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.step-card {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    height: 100%;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--fpl-purple), #4a0e4e);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h6 {
    color: var(--fpl-purple);
    margin-bottom: 0.5rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    height: 100%;
}

.feature-item i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.feature-item h6 {
    color: var(--fpl-purple);
}

.fpl-logo {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* RTL Support */
[dir="rtl"] .step-number {
    margin-right: 0;
    margin-left: 1rem;
}

[dir="rtl"] .feature-item i {
    margin-right: 0;
    margin-left: 1rem;
}

@media (max-width: 768px) {
    .step-card,
    .feature-item {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .feature-item i {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}
</style>

@push('scripts')
<script>
$(document).ready(function() {
    $('#connectForm').on('submit', function() {
        $('#connectBtn').prop('disabled', true).html(`
            <div class="loading-spinner me-2"></div>
            {{ __('app.connecting') }}...
        `);
    });
    
    // Auto-format FPL ID input
    $('#fpl_id').on('input', function() {
        let value = $(this).val();
        if (value.length > 0 && !isNaN(value)) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
});
</script>
@endpush
@endsection
