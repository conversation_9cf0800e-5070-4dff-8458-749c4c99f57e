@extends('layouts.app')

@section('title', __('auth.login') . ' - Fantasy Premier League')

@push('styles')
<style>
:root {
    --auth-primary-color: {{ $themeSettings['auth_primary_color'] ?? 'var(--fpl-purple)' }};
    --auth-secondary-color: {{ $themeSettings['auth_secondary_color'] ?? 'var(--fpl-green)' }};
    --auth-gradient-start: {{ $themeSettings['auth_gradient_start'] ?? '#37003c' }};
    --auth-gradient-end: {{ $themeSettings['auth_gradient_end'] ?? '#4a0e4e' }};
    --auth-text-color: {{ $themeSettings['auth_text_color'] ?? '#ffffff' }};
    --auth-accent-color: {{ $themeSettings['auth_accent_color'] ?? '#00ff87' }};
}

.auth-wrapper {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--auth-gradient-start) 0%, var(--auth-gradient-end) 100%);
    display: flex;
    align-items: center;
    padding: 2rem 0;
}

.auth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(0,0,0,0.08);
    overflow: hidden;
    max-width: 400px;
    width: 100%;
    margin: 0 auto;
    border-bottom: 3px solid var(--auth-secondary-color);
}

.auth-header {
    background: linear-gradient(135deg, var(--auth-gradient-start) 0%, var(--auth-gradient-end) 100%);
    color: var(--auth-text-color);
    text-align: center;
    padding: 2.5rem 1.5rem 1.5rem;
}

.auth-logo {
    width: 65px;
    height: 65px;
    background: rgba(255,255,255,0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.2rem;
    font-size: 1.6rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid var(--auth-secondary-color);
    color: var(--auth-secondary-color);
}

.auth-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.4rem;
    letter-spacing: 0.5px;
    color: var(--auth-secondary-color);
}

.auth-subtitle {
    opacity: 0.9;
    font-size: 0.85rem;
    margin: 0;
    color: var(--auth-text-color);
}

.auth-body {
    padding: 2rem;
}

.form-control {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.form-control:focus {
    border-color: var(--auth-secondary-color);
    box-shadow: 0 0 0 0.15rem rgba(var(--auth-accent-color), 0.2);
}

.input-group-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-right: none;
    border-radius: 8px 0 0 8px;
    padding: 0.5rem;
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 8px 8px 0;
}

.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.form-check-input:checked {
    background-color: var(--auth-secondary-color);
    border-color: var(--auth-secondary-color);
}

.btn-primary {
    background: linear-gradient(135deg, var(--auth-secondary-color) 0%, var(--auth-accent-color) 100%);
    border: none;
    color: var(--auth-primary-color);
    font-weight: 600;
}

.form-label {
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 0.4rem;
    color: #495057;
}

.text-danger {
    font-size: 0.875rem;
}

/* Animation */
.auth-card {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Additional Styles */
.auth-form .form-group {
    margin-bottom: 1.2rem;
}

.auth-form a {
    color: #0072ff;
    transition: all 0.2s ease;
    font-size: 0.85rem;
}

.auth-form a:hover {
    color: #005bcc;
    text-decoration: none;
}

.form-check-label {
    font-size: 0.85rem;
    color: #6c757d;
}

.form-check-input {
    width: 0.9rem;
    height: 0.9rem;
}
</style>
@endpush

@section('content')
<div class="auth-wrapper">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7 col-sm-9">
                <div class="auth-card">
                    <div class="auth-header">
                        <div class="auth-logo">
                            <i class="fas fa-futbol"></i>
                        </div>
                        <h2 class="auth-title">{{ __('auth.login') }}</h2>
                        <p class="auth-subtitle">{{ __('auth.login_description') }}</p>
                    </div>
                    <div class="auth-body">
                        <!-- Session Status -->
                        @if (session('status'))
                            <div class="alert alert-success mb-4">
                                {{ session('status') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('login') }}" class="auth-form">
                            @csrf

                            <!-- Email Address -->
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">{{ __('auth.email') }}</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope text-muted"></i>
                                    </span>
                                    <input id="email" type="email" class="form-control @error('email') is-invalid @enderror"
                                           name="email" value="{{ old('email') }}" required autofocus autocomplete="username"
                                           placeholder="{{ __('auth.email') }}">
                                </div>
                                @error('email')
                                    <div class="text-danger mt-1 small">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Password -->
                            <div class="form-group mb-3">
                                <label for="password" class="form-label">{{ __('auth.password') }}</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                    <input id="password" type="password" class="form-control @error('password') is-invalid @enderror"
                                           name="password" required autocomplete="current-password"
                                           placeholder="{{ __('auth.password') }}">
                                </div>
                                @error('password')
                                    <div class="text-danger mt-1 small">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Remember Me -->
                            <div class="form-group mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember_me" name="remember">
                                    <label class="form-check-label" for="remember_me">
                                        {{ __('auth.remember_me') }}
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                {{ __('auth.login_button') }}
                            </button>

                            @if (Route::has('password.request'))
                                <div class="text-center mb-4">
                                    <a href="{{ route('password.request') }}" class="text-decoration-none text-muted">
                                        {{ __('auth.forgot_password') }}
                                    </a>
                                </div>
                            @endif

                            <div class="text-center">
                                <hr class="my-3">
                                <p class="text-muted mb-3">{{ __('auth.dont_have_account') }}</p>
                                <a href="{{ route('register') }}" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-user-plus me-2"></i>
                                    {{ __('auth.create_account') }}
                                </a>
                            </div>
                        </form>
                    </div>
            </div>
        </div>
    </div>
</div>
@endsection
