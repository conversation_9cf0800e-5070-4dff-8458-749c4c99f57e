@extends('layouts.fpl')

@section('title', 'لوحة التحكم - Fantasy Premier League')

@section('content')
<!-- Welcome Hero -->
<section class="fpl-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1>مرحباً {{ Auth::user()->name }}!</h1>
                <p>إدارة فريقك الخيالي وتتبع النقاط</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('fantasy.create') }}" class="btn btn-fpl-secondary btn-lg">
                        <i class="fas fa-users me-2"></i>
                        إدارة فريقي
                    </a>
                    <button class="btn btn-outline-light btn-lg" onclick="scrollToStats()">
                        <i class="fas fa-chart-line me-2"></i>
                        عرض الإحصائيات
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Stats -->
<section class="py-5">
    <div class="container">
        <div class="row g-4" id="statsContainer">
            <div class="col-lg-3 col-md-6">
                <div class="fpl-stat-card">
                    <div class="fpl-stat-number" id="teamsCount">
                        <div class="loading-spinner"></div>
                    </div>
                    <div class="fpl-stat-label">فرقي</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="fpl-stat-card">
                    <div class="fpl-stat-number" id="totalPoints">
                        <div class="loading-spinner"></div>
                    </div>
                    <div class="fpl-stat-label">النقاط الإجمالية</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="fpl-stat-card">
                    <div class="fpl-stat-number" id="averagePoints">
                        <div class="loading-spinner"></div>
                    </div>
                    <div class="fpl-stat-label">متوسط النقاط</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="fpl-stat-card">
                    <div class="fpl-stat-number" id="remainingBudget">
                        <div class="loading-spinner"></div>
                    </div>
                    <div class="fpl-stat-label">مليون متاح</div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- My Teams Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="fpl-card">
                <div class="fpl-card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        فرقي
                    </h3>
                </div>
                <div class="card-body p-4" id="teamsContainer">
                    <div class="text-center py-5" id="noTeamsMessage">
                        <div class="loading-spinner mb-4"></div>
                        <h4 class="mb-3">جاري تحميل فرقك...</h4>
                    </div>
                    <div id="teamsList" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="fpl-card h-100">
                <div class="card-body text-center p-4">
                    <i class="fas fa-plus-circle fa-3x mb-3" style="color: var(--fpl-purple);"></i>
                    <h5 class="card-title fw-bold">إنشاء فريق</h5>
                    <p class="card-text text-muted">ابدأ بإنشاء فريقك الخيالي</p>
                    <a href="{{ route('fantasy.create') }}" class="btn btn-fpl-primary">
                        <i class="fas fa-rocket me-2"></i>
                        ابدأ الآن
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="fpl-card h-100">
                <div class="card-body text-center p-4">
                    <i class="fas fa-chart-line fa-3x mb-3" style="color: var(--fpl-green);"></i>
                    <h5 class="card-title fw-bold">الإحصائيات</h5>
                    <p class="card-text text-muted">تتبع أداء فريقك</p>
                    <button class="btn btn-fpl-secondary" onclick="scrollToStats()">
                        <i class="fas fa-eye me-2"></i>
                        عرض الإحصائيات
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="fpl-card h-100">
                <div class="card-body text-center p-4">
                    <i class="fas fa-trophy fa-3x mb-3" style="color: var(--fpl-pink);"></i>
                    <h5 class="card-title fw-bold">الترتيب</h5>
                    <p class="card-text text-muted">شاهد ترتيبك بين اللاعبين</p>
                    <button class="btn btn-fpl-primary" disabled>
                        <i class="fas fa-medal me-2"></i>
                        قريباً
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // تحميل البيانات عند تحميل الصفحة
    loadUserStats();
    loadUserTeams();

    // تسجيل callbacks للتحديثات في الوقت الفعلي
    if (window.realTimeUpdates) {
        window.realTimeUpdates.onPointsUpdate(function(data) {
            // تحديث الإحصائيات تلقائياً
            animateNumber('#teamsCount', data.teams_count);
            animateNumber('#totalPoints', data.total_points);
            animateNumber('#averagePoints', data.average_points);
            animateNumber('#remainingBudget', data.remaining_budget.toFixed(1));
        });

        window.realTimeUpdates.onTeamUpdate(function(teams) {
            // تحديث قائمة الفرق تلقائياً
            if (teams.length === 0) {
                $('#noTeamsMessage').html(`
                    <i class="fas fa-users fa-4x mb-4" style="color: var(--fpl-purple); opacity: 0.3;"></i>
                    <h4 class="mb-3">لم تنشئ أي فريق بعد</h4>
                    <p class="text-muted mb-4">ابدأ بإنشاء فريقك الأول واختر أفضل اللاعبين</p>
                    <a href="{{ route('fantasy.create') }}" class="btn btn-fpl-primary btn-lg me-3">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء فريق جديد
                    </a>
                    <a href="{{ route('admin.players') }}" class="btn btn-fpl-secondary btn-lg">
                        <i class="fas fa-users-cog me-2"></i>
                        إدارة اللاعبين
                    </a>
                `).show();
                $('#teamsList').hide();
            } else {
                $('#noTeamsMessage').hide();
                displayTeams(teams);
                $('#teamsList').show();
            }
        });
    }
});

function loadUserStats() {
    $.ajax({
        url: '{{ route("api.user.stats") }}',
        method: 'GET',
        success: function(data) {
            // تحديث الإحصائيات مع تأثيرات بصرية
            animateNumber('#teamsCount', data.teams_count);
            animateNumber('#totalPoints', data.total_points);
            animateNumber('#averagePoints', data.average_points);
            animateNumber('#remainingBudget', data.remaining_budget.toFixed(1));
        },
        error: function() {
            $('#teamsCount').html('<i class="fas fa-exclamation-triangle text-danger"></i>');
            $('#totalPoints').html('<i class="fas fa-exclamation-triangle text-danger"></i>');
            $('#averagePoints').html('<i class="fas fa-exclamation-triangle text-danger"></i>');
            $('#remainingBudget').html('<i class="fas fa-exclamation-triangle text-danger"></i>');
        }
    });
}

function loadUserTeams() {
    $.ajax({
        url: '{{ route("api.user.teams") }}',
        method: 'GET',
        success: function(teams) {
            if (teams.length === 0) {
                $('#noTeamsMessage').html(`
                    <i class="fas fa-users fa-4x mb-4" style="color: var(--fpl-purple); opacity: 0.3;"></i>
                    <h4 class="mb-3">لم تنشئ أي فريق بعد</h4>
                    <p class="text-muted mb-4">ابدأ بإنشاء فريقك الأول واختر أفضل اللاعبين</p>
                    <a href="{{ route('fantasy.create') }}" class="btn btn-fpl-primary btn-lg me-3">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء فريق جديد
                    </a>
                    <a href="{{ route('admin.players') }}" class="btn btn-fpl-secondary btn-lg">
                        <i class="fas fa-users-cog me-2"></i>
                        إدارة اللاعبين
                    </a>
                `).show();
                $('#teamsList').hide();
            } else {
                $('#noTeamsMessage').hide();
                displayTeams(teams);
                $('#teamsList').show();
            }
        },
        error: function() {
            $('#noTeamsMessage').html(`
                <i class="fas fa-exclamation-triangle fa-4x mb-4 text-danger"></i>
                <h4 class="mb-3">حدث خطأ في تحميل الفرق</h4>
                <button class="btn btn-fpl-primary" onclick="loadUserTeams()">
                    <i class="fas fa-refresh me-2"></i>
                    إعادة المحاولة
                </button>
            `).show();
        }
    });
}

function displayTeams(teams) {
    let teamsHtml = '<div class="row">';

    teams.forEach(function(team) {
        teamsHtml += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="fpl-card h-100 team-card" data-team-id="${team.id}">
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title fw-bold mb-0">${team.name}</h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{{ route('fantasy.team', '') }}/${team.id}">
                                        <i class="fas fa-eye me-2"></i>عرض الفريق
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="editTeam(${team.id})">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteTeam(${team.id})">
                                        <i class="fas fa-trash me-2"></i>حذف
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <p class="text-muted mb-3">
                            <i class="fas fa-user me-2"></i>${team.manager_name}
                        </p>

                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="fw-bold text-primary">${team.total_points}</div>
                                <small class="text-muted">النقاط</small>
                            </div>
                            <div class="col-4">
                                <div class="fw-bold text-success">${team.total_value}م</div>
                                <small class="text-muted">القيمة</small>
                            </div>
                            <div class="col-4">
                                <div class="fw-bold text-info">${team.players_count}</div>
                                <small class="text-muted">لاعب</small>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="{{ route('fantasy.team', '') }}/${team.id}" class="btn btn-fpl-primary btn-sm">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                        </div>

                        <small class="text-muted mt-2 d-block">
                            <i class="fas fa-calendar me-1"></i>
                            تم الإنشاء: ${team.created_at}
                        </small>
                    </div>
                </div>
            </div>
        `;
    });

    teamsHtml += '</div>';
    $('#teamsList').html(teamsHtml);

    // إضافة تأثيرات بصرية للبطاقات
    $('.team-card').hover(
        function() {
            $(this).addClass('shadow-lg');
            $(this).css('transform', 'translateY(-3px)');
        },
        function() {
            $(this).removeClass('shadow-lg');
            $(this).css('transform', 'translateY(0)');
        }
    );
}

function animateNumber(selector, newValue) {
    const element = $(selector);
    const currentValue = parseInt(element.text()) || 0;

    if (currentValue !== newValue) {
        element.prop('Counter', currentValue).animate({
            Counter: newValue
        }, {
            duration: 1000,
            easing: 'swing',
            step: function(now) {
                element.text(Math.ceil(now));
            }
        });
    }
}

function deleteTeam(teamId) {
    if (confirm('هل أنت متأكد من حذف هذا الفريق؟ لا يمكن التراجع عن هذا الإجراء.')) {
        $.ajax({
            url: `/api/team/${teamId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    showAlert(response.message, 'success');
                    // إزالة البطاقة مع تأثير بصري
                    $(`.team-card[data-team-id="${teamId}"]`).fadeOut(500, function() {
                        $(this).remove();
                        // إعادة تحميل البيانات
                        loadUserStats();
                        loadUserTeams();
                    });
                } else {
                    showAlert(response.message, 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء حذف الفريق', 'danger');
            }
        });
    }
}

function editTeam(teamId) {
    // توجيه إلى صفحة التعديل
    window.location.href = `{{ route('fantasy.create') }}?edit=${teamId}`;
}

function animateNumber(selector, newValue) {
    const element = $(selector);
    const currentValue = parseFloat(element.text()) || 0;

    if (currentValue !== newValue) {
        element.css({
            'transform': 'scale(1.1)',
            'color': '#00ff85',
            'transition': 'all 0.3s ease'
        });

        setTimeout(() => {
            element.text(newValue);
            element.css({
                'transform': 'scale(1)',
                'color': ''
            });
        }, 150);
    }
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-fpl-success' : 'alert-fpl-danger';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('.container').first().prepend(alert);
    setTimeout(() => alert.fadeOut(), 5000);
}

function scrollToStats() {
    document.querySelector('.fpl-stat-card').scrollIntoView({
        behavior: 'smooth'
    });
}
</script>
@endsection
