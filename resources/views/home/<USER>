@extends('layouts.app')

@section('title', __('app.welcome_title'))
@section('description', __('app.welcome_description'))

@push('styles')
<style>
    /* Hero Section */
    .hero-section {
        min-height: 100vh;
        background: linear-gradient(135deg, var(--fpl-purple) 0%, #4a0e4e 50%, var(--fpl-dark) 100%);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
    }
    
    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;
        opacity: 0.1;
        z-index: 1;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }
    
    .hero-title {
        font-size: 2.8rem;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, white 0%, var(--fpl-green) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        font-weight: 300;
        margin-bottom: 1.5rem;
        opacity: 0.9;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
        opacity: 0.8;
        max-width: 500px;
    }
    
    .hero-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .hero-buttons .btn {
        font-size: 0.9rem;
        padding: 10px 25px;
    }
    
    /* Carousel Styles */
    .hero-carousel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }
    
    .carousel-item {
        height: 100vh;
        background-size: cover;
        background-position: center;
        position: relative;
    }
    
    .carousel-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(55, 0, 60, 0.8) 0%, rgba(26, 26, 26, 0.6) 100%);
    }
    
    .carousel-content {
        position: relative;
        z-index: 2;
        height: 100%;
        display: flex;
        align-items: center;
        color: white;
    }
    
    /* Stats Section */
    .stats-section {
        background: var(--fpl-green);
        color: var(--fpl-purple);
        padding: 4rem 0;
    }
    
    .stat-item {
        text-align: center;
        padding: 2rem 1rem;
    }
    
    .stat-number {
        font-size: 2.2rem;
        font-weight: 700;
        display: block;
        margin-bottom: 0.3rem;
    }

    .stat-label {
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }
    
    /* Features Section */
    .features-section {
        padding: 6rem 0;
        background: var(--fpl-light);
    }
    
    .feature-card {
        background: white;
        border-radius: 15px;
        padding: 2rem 1.5rem;
        text-align: center;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--fpl-purple) 0%, var(--fpl-pink) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 1.5rem;
        color: white;
    }

    .feature-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.8rem;
        color: var(--fpl-purple);
    }

    .feature-description {
        color: var(--fpl-gray);
        line-height: 1.5;
        font-size: 0.9rem;
    }
    
    /* News Section */
    .news-section {
        padding: 6rem 0;
        background: white;
    }
    
    .news-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .news-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
    
    .news-image {
        height: 200px;
        background-size: cover;
        background-position: center;
        position: relative;
    }
    
    .news-category {
        position: absolute;
        top: 15px;
        left: 15px;
        background: var(--fpl-green);
        color: var(--fpl-purple);
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .news-content {
        padding: 2rem;
    }
    
    .news-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.8rem;
        color: var(--fpl-purple);
        line-height: 1.3;
    }

    .news-description {
        color: var(--fpl-gray);
        margin-bottom: 1rem;
        line-height: 1.5;
        font-size: 0.9rem;
    }
    
    .news-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        color: var(--fpl-gray);
    }
    
    .news-source {
        font-weight: 600;
    }
    
    .news-date {
        opacity: 0.8;
    }
    
    /* CTA Section */
    .cta-section {
        background: linear-gradient(135deg, var(--fpl-purple) 0%, var(--fpl-pink) 100%);
        color: white;
        padding: 6rem 0;
        text-align: center;
    }
    
    .cta-title {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .cta-description {
        font-size: 1rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }
    
    /* Responsive */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }

        .hero-subtitle {
            font-size: 1rem;
        }

        .hero-description {
            font-size: 0.9rem;
        }

        .hero-buttons {
            justify-content: center;
        }

        .hero-buttons .btn {
            font-size: 0.8rem;
            padding: 8px 20px;
        }

        .stat-number {
            font-size: 1.8rem;
        }

        .stat-label {
            font-size: 0.8rem;
        }

        .cta-title {
            font-size: 1.8rem;
        }

        .cta-description {
            font-size: 0.9rem;
        }

        .feature-card {
            padding: 1.5rem 1rem;
        }

        .feature-title {
            font-size: 1.1rem;
        }

        .feature-description {
            font-size: 0.85rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Hero Carousel Section -->
<section class="hero-section" id="hero">
    <div id="heroCarousel" class="carousel slide hero-carousel" data-bs-ride="carousel" data-bs-interval="5000">
        <div class="carousel-inner">
            @foreach($hero_slides as $index => $slide)
            <div class="carousel-item {{ $index === 0 ? 'active' : '' }}" style="background-image: url('{{ $slide['image'] }}')">
                <div class="carousel-content">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="hero-content" data-aos="fade-up" data-aos-delay="{{ $index * 200 }}">
                                    <h1 class="hero-title">{{ $slide['title'] }}</h1>
                                    <p class="hero-subtitle">{{ $slide['subtitle'] }}</p>
                                    <p class="hero-description">{{ $slide['description'] }}</p>
                                    <div class="hero-buttons">
                                        <a href="{{ $slide['cta_link'] }}" class="btn btn-fpl-primary">
                                            <i class="fas fa-play me-2"></i>
                                            {{ $slide['cta_text'] }}
                                        </a>
                                        <a href="#features" class="btn btn-fpl-outline">
                                            <i class="fas fa-info-circle me-2"></i>
                                            {{ __('app.learn_more') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <!-- Carousel Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
        </button>
        
        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
            @foreach($hero_slides as $index => $slide)
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="{{ $index }}" 
                    class="{{ $index === 0 ? 'active' : '' }}"></button>
            @endforeach
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="row">
            @foreach($stats as $stat)
            <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
                <div class="stat-item" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <i class="{{ $stat['icon'] }} mb-3" style="font-size: 2rem;"></i>
                    <span class="stat-number" data-count="{{ str_replace(['+', 'M', 'K'], '', $stat['number']) }}">{{ $stat['number'] }}</span>
                    <span class="stat-label">{{ $stat['label'] }}</span>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section" id="features">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="h3 fw-bold mb-2" data-aos="fade-up">{{ __('app.features_title') }}</h2>
                <p class="text-muted" data-aos="fade-up" data-aos-delay="100">{{ __('app.features_subtitle') }}</p>
            </div>
        </div>
        
        <div class="row g-4">
            @foreach($features as $feature)
            <div class="col-lg-4 col-md-6">
                <div class="feature-card" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="feature-icon">
                        <i class="{{ $feature['icon'] }}"></i>
                    </div>
                    <h4 class="feature-title">{{ $feature['title'] }}</h4>
                    <p class="feature-description">{{ $feature['description'] }}</p>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- News Section -->
<section class="news-section" id="news">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="h3 fw-bold mb-2" data-aos="fade-up">{{ __('app.latest_news') }}</h2>
                <p class="text-muted" data-aos="fade-up" data-aos-delay="100">{{ __('app.news_subtitle') }}</p>
            </div>
        </div>

        <div class="row g-4">
            @foreach($latest_news as $news)
            <div class="col-lg-4 col-md-6">
                <article class="news-card" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="news-image" style="background-image: url('{{ $news['image'] }}')">
                        <span class="news-category">{{ ucfirst($news['category']) }}</span>
                    </div>
                    <div class="news-content">
                        <h5 class="news-title">{{ $news['title'] }}</h5>
                        <p class="news-description">{{ $news['description'] }}</p>
                        <div class="news-meta">
                            <span class="news-source">
                                <i class="fas fa-newspaper me-1"></i>
                                {{ $news['source'] }}
                            </span>
                            <span class="news-date">
                                <i class="fas fa-clock me-1"></i>
                                {{ $news['published_at'] }}
                            </span>
                        </div>
                        <div class="mt-3">
                            <a href="{{ $news['url'] }}" target="_blank" class="btn btn-fpl-secondary btn-sm">
                                {{ __('app.read_more') }}
                                <i class="fas fa-external-link-alt ms-1"></i>
                            </a>
                        </div>
                    </div>
                </article>
            </div>
            @endforeach
        </div>

        <div class="row mt-4">
            <div class="col-12 text-center">
                <button class="btn btn-fpl-primary" onclick="loadMoreNews()">
                    <i class="fas fa-plus me-2"></i>
                    {{ __('app.load_more_news') }}
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Register Section -->
<section class="cta-section" id="register">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="cta-title" data-aos="fade-up">{{ __('app.ready_to_play') }}</h2>
                <p class="cta-description" data-aos="fade-up" data-aos-delay="100">
                    {{ __('app.register_description') }}
                </p>
                <div class="d-flex gap-2 justify-content-center flex-wrap" data-aos="fade-up" data-aos-delay="200">
                    @auth
                        @if(Auth::user()->hasFPLAccount())
                            <a href="{{ route('fpl.profile') }}" class="btn btn-fpl-primary">
                                <i class="fas fa-user-circle me-2"></i>
                                عرض ملفي الشخصي
                            </a>
                            <a href="{{ route('fpl.profile') }}" class="btn btn-fpl-outline">
                                <i class="fas fa-chart-line me-2"></i>
                                إحصائياتي
                            </a>
                        @else
                            <a href="{{ route('fpl.connect') }}" class="btn btn-fpl-primary">
                                <i class="fas fa-link me-2"></i>
                                ربط حساب FPL
                            </a>
                            <a href="https://fantasy.premierleague.com/" target="_blank" class="btn btn-fpl-outline">
                                <i class="fas fa-external-link-alt me-2"></i>
                                إنشاء حساب FPL
                            </a>
                        @endif
                    @else
                        <a href="{{ route('register') }}" class="btn btn-fpl-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب
                        </a>
                        <a href="{{ route('login') }}" class="btn btn-fpl-outline">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Demo Modal -->
<div class="modal fade" id="demoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app.demo_video') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="ratio ratio-16x9">
                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                            title="Fantasy Premier League Demo"
                            allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Load more news functionality
    let newsPage = 1;

    function loadMoreNews() {
        const btn = event.target;
        const originalText = btn.innerHTML;

        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{{ __("app.loading") }}...';
        btn.disabled = true;

        fetch(`/api/news?page=${++newsPage}&limit=6`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.length > 0) {
                    const newsContainer = document.querySelector('.news-section .row.g-4');

                    data.data.forEach((news, index) => {
                        const newsCard = createNewsCard(news, index);
                        newsContainer.insertAdjacentHTML('beforeend', newsCard);
                    });

                    // Animate new cards
                    AOS.refresh();
                } else {
                    btn.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading news:', error);
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
    }

    function createNewsCard(news, index) {
        return `
            <div class="col-lg-4 col-md-6">
                <article class="news-card" data-aos="fade-up" data-aos-delay="${index * 100}">
                    <div class="news-image" style="background-image: url('${news.image}')">
                        <span class="news-category">${news.category.charAt(0).toUpperCase() + news.category.slice(1)}</span>
                    </div>
                    <div class="news-content">
                        <h5 class="news-title">${news.title}</h5>
                        <p class="news-description">${news.description}</p>
                        <div class="news-meta">
                            <span class="news-source">
                                <i class="fas fa-newspaper me-1"></i>
                                ${news.source}
                            </span>
                            <span class="news-date">
                                <i class="fas fa-clock me-1"></i>
                                ${news.published_at}
                            </span>
                        </div>
                        <div class="mt-3">
                            <a href="${news.url}" target="_blank" class="btn btn-fpl-secondary btn-sm">
                                {{ __('app.read_more') }}
                                <i class="fas fa-external-link-alt ms-1"></i>
                            </a>
                        </div>
                    </div>
                </article>
            </div>
        `;
    }

    // Counter animation for stats
    function animateCounters() {
        const counters = document.querySelectorAll('[data-count]');

        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-count'));
            const originalText = counter.textContent;
            let current = 0;
            const increment = target / 100;

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.ceil(current) + originalText.replace(/\d+/, '');
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = originalText;
                }
            };

            // Start animation when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCounter();
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(counter);
        });
    }

    // Initialize counter animation
    document.addEventListener('DOMContentLoaded', animateCounters);

    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
</script>
@endpush
