<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ \App\Helpers\LanguageHelper::getDirection() }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="@yield('description', 'Fantasy Premier League - Build your dream team and compete with millions of players worldwide')">
    <meta name="keywords" content="Fantasy Premier League, FPL, Football, Soccer, Premier League, Fantasy Football">
    <meta name="author" content="Fantasy Premier League">

    <title>@yield('title', 'Fantasy Premier League - Official Fantasy Football Game')</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --fpl-purple: #37003c;
            --fpl-green: #00ff87;
            --fpl-pink: #ff0050;
            --fpl-blue: #0070f3;
            --fpl-dark: #1a1a1a;
            --fpl-light: #f8f9fa;
            --fpl-gray: #6c757d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        /* Header Styles */
        .navbar-fpl {
            background: linear-gradient(135deg, var(--fpl-purple) 0%, #4a0e4e 100%);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .navbar-fpl.scrolled {
            background: rgba(55, 0, 60, 0.95);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            text-decoration: none !important;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
        }

        .navbar-brand:hover {
            transform: translateY(-2px);
            text-decoration: none !important;
        }

        .navbar-brand img {
            transition: all 0.3s ease;
            display: block;
            max-width: none;
            width: auto;
            filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .navbar-brand:hover img {
            transform: scale(1.05);
            filter: brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(90deg) drop-shadow(0 4px 8px rgba(0,255,135,0.4));
        }

        /* Fallback logo styles */
        .navbar-brand > div {
            transition: all 0.3s ease;
        }

        .navbar-brand:hover > div {
            transform: scale(1.02);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 400;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            position: relative;
            padding: 8px 12px !important;
        }

        .nav-link:hover {
            color: var(--fpl-green) !important;
            transform: translateY(-1px);
        }

        .nav-link.active {
            color: var(--fpl-green) !important;
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background: var(--fpl-green);
            border-radius: 1px;
        }

        /* Language Selector */
        .language-selector .dropdown-toggle {
            background: transparent;
            border: 1px solid var(--fpl-green);
            color: white;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .language-selector .dropdown-toggle:hover {
            background: var(--fpl-green);
            color: var(--fpl-purple);
            border-color: var(--fpl-green);
        }

        .language-selector .dropdown-menu {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .language-selector .dropdown-item {
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .language-selector .dropdown-item:hover {
            background: var(--fpl-green);
            color: var(--fpl-purple);
        }

        .language-selector .dropdown-item.active {
            background: var(--fpl-purple);
            color: white;
        }

        /* Buttons */
        .btn-fpl-primary {
            background: linear-gradient(135deg, var(--fpl-green) 0%, #00e676 100%);
            border: none;
            color: var(--fpl-purple);
            font-weight: 500;
            padding: 8px 20px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            letter-spacing: 0.3px;
        }

        .btn-fpl-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(0, 255, 135, 0.3);
            color: var(--fpl-purple);
        }

        .btn-fpl-secondary {
            background: transparent;
            border: 1px solid var(--fpl-purple);
            color: var(--fpl-purple);
            font-weight: 500;
            padding: 8px 20px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            letter-spacing: 0.3px;
        }

        .btn-fpl-secondary:hover {
            background: var(--fpl-purple);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(55, 0, 60, 0.3);
        }

        .btn-fpl-outline {
            background: transparent;
            border: 1px solid white;
            color: white;
            font-weight: 500;
            padding: 8px 20px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            letter-spacing: 0.3px;
        }

        .btn-fpl-outline:hover {
            background: white;
            color: var(--fpl-purple);
            transform: translateY(-1px);
        }

        /* Footer */
        footer {
            background: var(--fpl-dark) !important;
        }

        footer a:hover {
            color: var(--fpl-green) !important;
            transition: color 0.3s ease;
        }

        footer .fab {
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        footer .fab:hover {
            transform: translateY(-2px);
            color: var(--fpl-green) !important;
        }

        /* Footer Logo */
        footer .footer-logo {
            cursor: pointer;
            filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(0,0,0,0.3));
            transition: all 0.3s ease;
            display: block;
            max-width: none;
            width: auto;
        }

        footer .footer-logo:hover {
            transform: scale(1.05);
            filter: brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(90deg) drop-shadow(0 4px 8px rgba(0,255,135,0.4));
        }

        footer a:hover {
            text-decoration: none;
        }

        /* Footer Social Links */
        footer .d-flex a {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        footer .d-flex a:hover {
            background: var(--fpl-green);
            color: var(--fpl-purple) !important;
            transform: translateY(-2px);
        }

        /* Scroll to Top Button */
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--fpl-green);
            color: var(--fpl-purple);
            border: none;
            border-radius: 50%;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(0, 255, 135, 0.3);
        }

        .scroll-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .scroll-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 255, 135, 0.4);
        }

        [dir="rtl"] .scroll-to-top {
            right: auto;
            left: 30px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .navbar-brand img {
                height: 35px;
            }

            .nav-link {
                padding: 8px 0;
                font-size: 0.85rem;
            }

            .btn-fpl-primary,
            .btn-fpl-secondary,
            .btn-fpl-outline {
                padding: 6px 15px;
                font-size: 0.8rem;
            }

            .language-selector .dropdown-toggle {
                padding: 4px 8px;
                font-size: 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .navbar-brand img {
                height: 32px;
            }

            .navbar-nav {
                text-align: center;
            }

            .navbar-brand img {
                height: 38px !important;
            }

            .navbar-brand > div > div:first-child {
                width: 35px !important;
                height: 35px !important;
                font-size: 1rem !important;
            }

            .navbar-brand > div span {
                font-size: 1rem !important;
            }
        }

        /* Logo specific styles */
        .navbar-brand img {
            object-fit: contain !important;
            background: transparent !important;
            border: none !important;
            outline: none !important;
            vertical-align: middle;
        }

        /* Ensure logo visibility */
        .navbar-brand {
            min-width: 120px;
            min-height: 40px;
            display: flex !important;
            align-items: center !important;
        }
    </style>

    @stack('styles')
</head>

<body>
    <!-- Navigation -->
    @unless(request()->routeIs('login') || request()->routeIs('register') || request()->routeIs('password.*'))
    <nav class="navbar navbar-expand-lg navbar-fpl fixed-top" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <img src="{{ asset('images/fpl-logo.png') }}"
                     alt="Fantasy Premier League"
                     height="45"
                     style="max-width: none; width: auto; filter: brightness(0) invert(1);"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div style="display: none; align-items: center; color: white; text-decoration: none;">
                    <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #37003c 0%, #00ff87 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 1.2rem; font-weight: bold; color: white;">
                        FPL
                    </div>
                    <span style="font-size: 1.1rem; font-weight: bold; color: white;">Fantasy Premier League</span>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                            {{ __('app.home') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">
                            {{ __('app.features') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#news">
                            {{ __('app.news') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">
                            {{ __('app.about') }}
                        </a>
                    </li>
                    @auth
                    @endauth
                </ul>

                <!-- Auth Buttons -->
                <div class="d-flex align-items-center gap-2">
                    @guest
                        <a href="{{ route('login') }}" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                        <a href="{{ route('register') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-user-plus me-1"></i>
                            إنشاء حساب
                        </a>
                    @else
                        <div class="dropdown">
                            <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                {{ Auth::user()->name }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="{{ route('profile.personal') }}">
                                        <i class="fas fa-user-circle me-2"></i>
                                        ملفي الشخصي
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>
                                            تسجيل الخروج
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @endguest

                    <!-- Language Selector -->
                    <div class="language-selector dropdown">
                        <button class="btn dropdown-toggle btn-sm" type="button" data-bs-toggle="dropdown">
                            @php
                                $currentLang = \App\Helpers\LanguageHelper::getCurrentLanguage();
                            @endphp
                            {{ $currentLang['flag'] }} {{ $currentLang['name'] }}
                        </button>
                        <ul class="dropdown-menu">
                            @foreach(\App\Helpers\LanguageHelper::getSupportedLanguages() as $code => $language)
                                <li>
                                    <form method="POST" action="{{ route('set.language') }}" style="display: inline;">
                                        @csrf
                                        <input type="hidden" name="language" value="{{ $code }}">
                                        <button type="submit" class="dropdown-item {{ app()->getLocale() === $code ? 'active' : '' }}">
                                            {{ $language['flag'] }} {{ $language['name'] }}
                                        </button>
                                    </form>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    @endunless

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    @unless(request()->routeIs('login') || request()->routeIs('register') || request()->routeIs('password.*'))
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="mb-3">
                        <a href="{{ route('home') }}" class="d-inline-block">
                            <img src="{{ asset('images/logo.png') }}"
                                 alt="Fantasy Premier League"
                                 height="50"
                                 class="footer-logo">
                        </a>
                    </div>
                    <p class="text-muted">
                        {{ __('app.footer_description') }}
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">{{ __('app.quick_links') }}</h6>
                    <ul class="list-unstyled">
                        <li><a href="#features" class="text-muted text-decoration-none">{{ __('app.features') }}</a></li>
                        <li><a href="#news" class="text-muted text-decoration-none">{{ __('app.news') }}</a></li>
                        <li><a href="#about" class="text-muted text-decoration-none">{{ __('app.about') }}</a></li>
                        <li><a href="#register" class="text-muted text-decoration-none">{{ __('app.register') }}</a></li>
                    </ul>
                </div>

                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">{{ __('app.support') }}</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">{{ __('app.help_center') }}</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">{{ __('app.contact_us') }}</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">{{ __('app.faq') }}</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">{{ __('app.privacy_policy') }}</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 col-md-6">
                    <h6 class="fw-bold mb-3">{{ __('app.newsletter') }}</h6>
                    <p class="text-muted small">{{ __('app.newsletter_description') }}</p>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="{{ __('app.enter_email') }}">
                        <button class="btn btn-success" type="button">{{ __('app.subscribe') }}</button>
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted small mb-0">
                        &copy; {{ date('Y') }} Fantasy Premier League. {{ __('app.all_rights_reserved') }}
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted small mb-0">
                        {{ __('app.made_with') }} <i class="fas fa-heart text-danger"></i> {{ __('app.for_football_fans') }}
                    </p>
                </div>
            </div>
        </div>
    </footer>
    @endunless

    <!-- Scroll to Top Button -->
    @unless(request()->routeIs('login') || request()->routeIs('register') || request()->routeIs('password.*'))
    <button class="scroll-to-top" id="scrollToTop">
        <i class="fas fa-arrow-up"></i>
    </button>
    @endunless

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JS -->
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('mainNavbar');
            const scrollToTopBtn = document.getElementById('scrollToTop');

            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Show/hide scroll to top button if it exists
            if (scrollToTopBtn) {
                if (window.scrollY > 300) {
                    scrollToTopBtn.classList.add('visible');
                } else {
                    scrollToTopBtn.classList.remove('visible');
                }
            }
        });

        // Scroll to top functionality
        const scrollTopButton = document.getElementById('scrollToTop');
        if (scrollTopButton) {
            scrollTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    </script>

    @stack('scripts')
</body>
</html>
