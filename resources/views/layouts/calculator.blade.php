<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ \App\Helpers\LanguageHelper::getDirection() }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', __('app.points_calculator')) - Fantasy Premier League</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap CSS -->
    @if(\App\Helpers\LanguageHelper::isRTL())
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    @else
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    @endif

    <!-- Custom CSS -->
    <link href="{{ asset('css/calculator.css') }}" rel="stylesheet">
    
    <style>
        :root {
            --fpl-purple: #38003c;
            --fpl-green: #00ff85;
            --fpl-pink: #ff0050;
            --fpl-cyan: #00d4ff;
            --fpl-text-light: #ffffff;
            --fpl-text-dark: #38003c;
            --fpl-bg-light: #f8f9fa;
        }

        body {
            font-family: 'Figtree', sans-serif;
            background: linear-gradient(135deg, var(--fpl-purple) 0%, #4a0e4e 100%);
            min-height: 100vh;
            color: var(--fpl-text-dark);
        }

        /* Navigation */
        .navbar-fpl {
            background: rgba(56, 0, 60, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--fpl-green);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--fpl-text-light) !important;
        }

        .nav-link {
            color: var(--fpl-text-light) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem !important;
            border-radius: 6px;
        }

        .nav-link:hover {
            color: var(--fpl-green) !important;
            background: rgba(0, 255, 133, 0.1);
        }

        .nav-link.active {
            color: var(--fpl-green) !important;
            background: rgba(0, 255, 133, 0.2);
        }

        /* Language Selector */
        .language-selector .dropdown-toggle {
            background: transparent;
            border: 1px solid var(--fpl-green);
            color: var(--fpl-text-light);
            border-radius: 6px;
            padding: 0.5rem 1rem;
        }

        .language-selector .dropdown-toggle:hover {
            background: var(--fpl-green);
            color: var(--fpl-purple);
        }

        .language-selector .dropdown-menu {
            background: var(--fpl-purple);
            border: 1px solid var(--fpl-green);
        }

        .language-selector .dropdown-item {
            color: var(--fpl-text-light);
        }

        .language-selector .dropdown-item:hover {
            background: var(--fpl-green);
            color: var(--fpl-purple);
        }

        /* Main Content */
        .main-content {
            background: var(--fpl-bg-light);
            min-height: calc(100vh - 80px);
            padding: 2rem 0;
        }

        /* Cards */
        .fpl-card {
            background: white;
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .fpl-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .fpl-card-header {
            background: linear-gradient(135deg, var(--fpl-purple), #4a0e4e);
            color: var(--fpl-text-light);
            padding: 1.5rem;
            border: none;
        }

        /* Buttons */
        .btn-fpl-primary {
            background: linear-gradient(135deg, var(--fpl-purple), #4a0e4e);
            border: none;
            color: var(--fpl-text-light);
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(56, 0, 60, 0.3);
        }

        .btn-fpl-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(56, 0, 60, 0.4);
            color: var(--fpl-text-light);
        }

        .btn-fpl-secondary {
            background: linear-gradient(135deg, var(--fpl-cyan), #00b8e6);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        .btn-fpl-secondary:hover {
            background: linear-gradient(135deg, #00b8e6, #00a3cc);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
            color: white;
        }

        .btn-fpl-success {
            background: linear-gradient(135deg, var(--fpl-green), #00e676);
            border: none;
            color: var(--fpl-purple);
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 255, 133, 0.3);
        }

        .btn-fpl-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 255, 133, 0.4);
            color: var(--fpl-purple);
        }

        /* Form Controls */
        .form-control:focus {
            border-color: var(--fpl-green);
            box-shadow: 0 0 0 0.2rem rgba(0, 255, 133, 0.25);
        }

        .form-select:focus {
            border-color: var(--fpl-green);
            box-shadow: 0 0 0 0.2rem rgba(0, 255, 133, 0.25);
        }

        /* Results */
        .result-card {
            background: linear-gradient(135deg, var(--fpl-green), #00e676);
            color: var(--fpl-purple);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
        }

        .total-points {
            font-size: 3rem;
            font-weight: 700;
            margin: 1rem 0;
        }

        .breakdown-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .breakdown-item.positive {
            border-left: 4px solid var(--fpl-green);
        }

        .breakdown-item.negative {
            border-left: 4px solid var(--fpl-pink);
        }

        .breakdown-item.neutral {
            border-left: 4px solid #6c757d;
        }

        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--fpl-green);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Footer */
        .footer {
            background: var(--fpl-purple);
            color: var(--fpl-text-light);
            padding: 2rem 0;
            margin-top: 3rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .total-points {
                font-size: 2rem;
            }
            
            .navbar-brand {
                font-size: 1.2rem;
            }
        }

        /* RTL Specific */
        [dir="rtl"] .navbar-nav {
            margin-right: auto;
            margin-left: 0;
        }

        [dir="rtl"] .breakdown-item.positive {
            border-right: 4px solid var(--fpl-green);
            border-left: none;
        }

        [dir="rtl"] .breakdown-item.negative {
            border-right: 4px solid var(--fpl-pink);
            border-left: none;
        }

        [dir="rtl"] .breakdown-item.neutral {
            border-right: 4px solid #6c757d;
            border-left: none;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-fpl fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('calculator.index') }}">
                <i class="fas fa-calculator me-2"></i>
                FPL Calculator
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}"
                           href="{{ route('home') }}">
                            <i class="fas fa-home me-1"></i>
                            {{ __('app.home') }}
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('calculator.calculator') ? 'active' : '' }}"
                           href="{{ route('calculator.calculator') }}">
                            <i class="fas fa-calculator me-1"></i>
                            {{ __('app.calculator') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('calculator.rules') ? 'active' : '' }}"
                           href="{{ route('calculator.rules') }}">
                            <i class="fas fa-book me-1"></i>
                            {{ __('app.rules') }}
                        </a>
                    </li>

                </ul>
                
                <!-- Language Selector -->
                <div class="language-selector dropdown">
                    <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        @php
                            $currentLang = \App\Helpers\LanguageHelper::getCurrentLanguage();
                        @endphp
                        {{ $currentLang['flag'] }} {{ $currentLang['name'] }}
                    </button>
                    <ul class="dropdown-menu">
                        @foreach(\App\Helpers\LanguageHelper::getSupportedLanguages() as $code => $language)
                            <li>
                                <form method="POST" action="{{ route('calculator.setLanguage') }}" style="display: inline;">
                                    @csrf
                                    <input type="hidden" name="language" value="{{ $code }}">
                                    <button type="submit" class="dropdown-item {{ app()->getLocale() === $code ? 'active' : '' }}">
                                        {{ $language['flag'] }} {{ $language['name'] }}
                                    </button>
                                </form>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" style="margin-top: 80px;">
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h5>{{ __('app.footer_text') }}</h5>
                    <p class="mb-0">{{ __('app.footer_desc') }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <p class="mb-0">
                        <i class="fas fa-code me-1"></i>
                        Made with ❤️ for FPL fans
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Calculator JS -->
    <script src="{{ asset('js/calculator.js') }}"></script>

    <script>
        // إعداد CSRF token للطلبات AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>
    
    @stack('scripts')
</body>
</html>
