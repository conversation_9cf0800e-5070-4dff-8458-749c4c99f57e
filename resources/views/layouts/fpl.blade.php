<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Fantasy Premier League')</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Premier League Official Font -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Official FPL Colors */
            --fpl-purple: #38003c;
            --fpl-green: #00ff85;
            --fpl-pink: #ff0050;
            --fpl-cyan: #00d4ff;
            --fpl-dark: #1a1a1a;
            --fpl-light-gray: #f8f9fa;
            --fpl-border: #e0e0e0;
            --fpl-text-light: #ffffff;
            --fpl-text-dark: #333333;
            --fpl-gradient-primary: linear-gradient(135deg, #38003c 0%, #ff0050 100%);
            --fpl-gradient-secondary: linear-gradient(135deg, #00ff85 0%, #00d4ff 100%);
        }

        * {
            font-family: 'Poppins', 'Cairo', sans-serif;
            box-sizing: border-box;
        }

        body {
            background: var(--fpl-purple);
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(255, 0, 80, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 133, 0.1) 0%, transparent 50%);
            min-height: 100vh;
            color: var(--fpl-text-dark);
            margin: 0;
            padding: 0;
        }

        /* Header Styles - Exact FPL Match */
        .fpl-header {
            background: var(--fpl-purple);
            border-bottom: 3px solid var(--fpl-green);
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .fpl-navbar {
            padding: 0;
        }

        .fpl-navbar .navbar-brand {
            color: var(--fpl-text-light) !important;
            font-weight: 800;
            font-size: 1.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .fpl-navbar .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 600;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 1rem 1.5rem !important;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .fpl-navbar .nav-link:hover,
        .fpl-navbar .nav-link.active {
            color: var(--fpl-green) !important;
            border-bottom-color: var(--fpl-green);
            background: rgba(0, 255, 133, 0.1);
        }

        /* Main Content Container */
        .fpl-main {
            background: var(--fpl-text-light);
            min-height: calc(100vh - 80px);
        }

        /* FPL Card Styles */
        .fpl-card {
            background: white;
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .fpl-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--fpl-green);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Alert Styles */
        .alert-fpl-success {
            background-color: rgba(0, 255, 133, 0.1);
            border-color: var(--fpl-green);
            color: var(--fpl-green);
        }

        .alert-fpl-danger {
            background-color: rgba(255, 0, 80, 0.1);
            border-color: var(--fpl-pink);
            color: var(--fpl-pink);
        }

        /* Team Card Animations */
        .team-card {
            transition: all 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(56, 0, 60, 0.2);
        }

        /* Player Card Selected State */
        .player-card.selected {
            border: 2px solid var(--fpl-green);
            background: linear-gradient(135deg, rgba(0, 255, 133, 0.1), rgba(0, 212, 255, 0.1));
        }

        .fpl-card-header {
            background: var(--fpl-gradient-primary);
            color: var(--fpl-text-light);
            padding: 1.5rem;
            border: none;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Buttons - FPL Style */
        .btn-fpl-primary {
            background: var(--fpl-gradient-primary);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: var(--fpl-text-light);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(56, 0, 60, 0.3);
        }

        .btn-fpl-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(56, 0, 60, 0.4);
            color: var(--fpl-text-light);
        }

        .btn-fpl-secondary {
            background: linear-gradient(135deg, var(--fpl-cyan), #00b8e6);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        .btn-fpl-secondary:hover {
            background: linear-gradient(135deg, #00b8e6, #00a3cc);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
            color: white;
        }

        .btn-fpl-secondary {
            background: var(--fpl-gradient-secondary);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: var(--fpl-dark);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 255, 133, 0.3);
        }

        .btn-fpl-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 255, 133, 0.4);
            color: var(--fpl-dark);
        }

        /* Hero Section */
        .fpl-hero {
            background: var(--fpl-gradient-primary);
            color: var(--fpl-text-light);
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .fpl-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }

        .fpl-hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .fpl-hero p {
            font-size: 1.3rem;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        /* Stats Cards */
        .fpl-stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid var(--fpl-green);
        }

        .fpl-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .fpl-stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--fpl-purple);
            margin-bottom: 0.5rem;
        }

        .fpl-stat-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--fpl-text-dark);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .fpl-hero h1 {
                font-size: 2.5rem;
            }
            
            .fpl-navbar .nav-link {
                padding: 0.8rem 1rem !important;
                font-size: 0.9rem;
            }
            
            .fpl-stat-number {
                font-size: 2rem;
            }
        }

        /* Loading Animation */
        .fpl-loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 255, 133, 0.3);
            border-radius: 50%;
            border-top-color: var(--fpl-green);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Alert Styles */
        .alert-fpl-success {
            background: linear-gradient(135deg, rgba(0, 255, 133, 0.1), rgba(0, 212, 255, 0.1));
            border: 2px solid var(--fpl-green);
            border-radius: 12px;
            color: var(--fpl-dark);
        }

        .alert-fpl-danger {
            background: linear-gradient(135deg, rgba(255, 0, 80, 0.1), rgba(56, 0, 60, 0.1));
            border: 2px solid var(--fpl-pink);
            border-radius: 12px;
            color: var(--fpl-dark);
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Header -->
    <header class="fpl-header">
        <nav class="navbar navbar-expand-lg fpl-navbar">
            <div class="container">
                <a class="navbar-brand" href="{{ route('dashboard') }}">
                    <i class="fas fa-futbol me-2"></i>
                    Fantasy Premier League
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                                <i class="fas fa-home me-1"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fantasy.*') ? 'active' : '' }}" href="{{ route('fantasy.create') }}">
                                <i class="fas fa-users me-1"></i>
                                فريقي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-trophy me-1"></i>
                                الترتيب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-chart-line me-1"></i>
                                الإحصائيات
                            </a>
                        </li>
                    </ul>
                    
                    <ul class="navbar-nav">
                        @auth
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>
                                    {{ Auth::user()->name }}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{{ route('profile.edit') }}">الملف الشخصي</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" class="dropdown-item">تسجيل الخروج</button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        @else
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('login') }}">تسجيل الدخول</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('register') }}">إنشاء حساب</a>
                            </li>
                        @endauth
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="fpl-main">
        @if(session('success'))
            <div class="container mt-3">
                <div class="alert alert-fpl-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="container mt-3">
                <div class="alert alert-fpl-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        @endif

        @yield('content')
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Real-time Updates -->
    <script src="{{ asset('js/real-time-updates.js') }}"></script>
    
    <script>
        // إعداد CSRF token للطلبات AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // تأثيرات التحميل
        $(document).ready(function() {
            // إخفاء شاشة التحميل عند اكتمال التحميل
            $('.fpl-loading').fadeOut();
            
            // تأثير hover للبطاقات
            $('.fpl-card').hover(
                function() { $(this).addClass('shadow-lg'); },
                function() { $(this).removeClass('shadow-lg'); }
            );
        });
    </script>
    
    @stack('scripts')
</body>
</html>
