@extends('layouts.calculator')

@section('title', __('app.home'))

@section('content')
<div class="container">
    <!-- Hero Section -->
    <div class="row align-items-center min-vh-100">
        <div class="col-lg-6">
            <div class="hero-content">
                <h1 class="display-4 fw-bold text-white mb-4">
                    {{ __('app.welcome_title') }}
                </h1>
                <p class="lead text-white-50 mb-5">
                    {{ __('app.welcome_subtitle') }}
                </p>
                <div class="d-flex gap-3 flex-wrap">
                    @if(Session::has('fpl_user_id'))
                        <a href="{{ route('fpl.dashboard') }}" class="btn btn-fpl-success btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            {{ __('app.dashboard') }}
                        </a>
                        <a href="{{ route('fpl.players') }}" class="btn btn-fpl-secondary btn-lg">
                            <i class="fas fa-users me-2"></i>
                            {{ __('app.all_players') }}
                        </a>
                    @else
                        <a href="{{ route('fpl.connect') }}" class="btn btn-fpl-success btn-lg">
                            <i class="fas fa-link me-2"></i>
                            {{ __('app.connect_fpl_account') }}
                        </a>
                        <a href="{{ route('calculator.calculator') }}" class="btn btn-fpl-secondary btn-lg">
                            <i class="fas fa-calculator me-2"></i>
                            {{ __('app.calculator') }}
                        </a>
                    @endif
                    <a href="{{ route('calculator.rules') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-book me-2"></i>
                        {{ __('app.view_rules') }}
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="hero-image text-center">
                <div class="calculator-preview fpl-card p-4">
                    <div class="fpl-card-header text-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            {{ __('app.points_calculator') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="stat-box">
                                    <div class="stat-icon">
                                        <i class="fas fa-futbol text-success"></i>
                                    </div>
                                    <div class="stat-label">{{ __('app.goals') }}</div>
                                    <div class="stat-value">2</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-box">
                                    <div class="stat-icon">
                                        <i class="fas fa-hands-helping text-info"></i>
                                    </div>
                                    <div class="stat-label">{{ __('app.assists') }}</div>
                                    <div class="stat-value">1</div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="result-preview">
                                    <div class="total-points-preview">
                                        <span class="points-label">{{ __('app.total_points') }}</span>
                                        <span class="points-value">13</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- How It Works Section -->
<div class="container py-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold text-white">{{ __('app.how_it_works') }}</h2>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-4">
            <div class="fpl-card h-100 text-center">
                <div class="card-body p-4">
                    <div class="step-icon mb-3">
                        <i class="fas fa-edit fa-3x" style="color: var(--fpl-purple);"></i>
                    </div>
                    <h5 class="fw-bold">{{ __('app.step_1') }}</h5>
                    <p class="text-muted">{{ __('app.step_1_desc') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="fpl-card h-100 text-center">
                <div class="card-body p-4">
                    <div class="step-icon mb-3">
                        <i class="fas fa-cogs fa-3x" style="color: var(--fpl-green);"></i>
                    </div>
                    <h5 class="fw-bold">{{ __('app.step_2') }}</h5>
                    <p class="text-muted">{{ __('app.step_2_desc') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="fpl-card h-100 text-center">
                <div class="card-body p-4">
                    <div class="step-icon mb-3">
                        <i class="fas fa-chart-line fa-3x" style="color: var(--fpl-cyan);"></i>
                    </div>
                    <h5 class="fw-bold">{{ __('app.step_3') }}</h5>
                    <p class="text-muted">{{ __('app.step_3_desc') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container py-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-6 fw-bold text-white">Features</h2>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-6 col-lg-3">
            <div class="feature-card text-center text-white">
                <i class="fas fa-globe fa-2x mb-3" style="color: var(--fpl-green);"></i>
                <h6>Multi-Language</h6>
                <p class="small">English, Arabic, French</p>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3">
            <div class="feature-card text-center text-white">
                <i class="fas fa-mobile-alt fa-2x mb-3" style="color: var(--fpl-cyan);"></i>
                <h6>Responsive</h6>
                <p class="small">Works on all devices</p>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3">
            <div class="feature-card text-center text-white">
                <i class="fas fa-bolt fa-2x mb-3" style="color: var(--fpl-pink);"></i>
                <h6>Instant</h6>
                <p class="small">Real-time calculations</p>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3">
            <div class="feature-card text-center text-white">
                <i class="fas fa-shield-alt fa-2x mb-3" style="color: var(--fpl-green);"></i>
                <h6>Accurate</h6>
                <p class="small">Official FPL rules</p>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--fpl-purple);
}

.result-preview {
    background: linear-gradient(135deg, var(--fpl-green), #00e676);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.total-points-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--fpl-purple);
}

.points-label {
    font-weight: 600;
}

.points-value {
    font-size: 2rem;
    font-weight: bold;
}

.feature-card {
    padding: 2rem 1rem;
}

.calculator-preview {
    max-width: 400px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .hero-content {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .points-value {
        font-size: 1.5rem;
    }
}
</style>
@endsection
