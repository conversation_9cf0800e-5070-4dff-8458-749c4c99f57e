<div class="fpl-card">
    <div class="fpl-card-header text-center">
        <h5 class="mb-0">
            <i class="fas fa-trophy me-2"></i>
            {{ __('app.results') }}
        </h5>
    </div>
    <div class="card-body">
        <div class="result-card">
            <h6 class="mb-2">{{ $result['player_name'] }}</h6>
            <div class="position-badge mb-2">
                <span class="badge bg-light text-dark">{{ $result['position'] }}</span>
            </div>
            <div class="total-points">{{ $result['total_points'] }}</div>
            <p class="mb-0">{{ __('app.total_points') }}</p>
        </div>
        
        <h6 class="fw-bold mb-3 mt-4">{{ __('app.points_breakdown') }}</h6>
        <div class="breakdown-list">
            @foreach($result['breakdown'] as $key => $value)
                @if($value != 0)
                    <div class="breakdown-item {{ $value > 0 ? 'positive' : ($value < 0 ? 'negative' : 'neutral') }}">
                        <span>
                            <i class="{{ getBreakdownIcon($key) }} me-2"></i>
                            {{ getBreakdownLabel($key) }}
                        </span>
                        <span class="fw-bold">{{ $value > 0 ? '+' : '' }}{{ $value }}</span>
                    </div>
                @endif
            @endforeach
            
            @if(collect($result['breakdown'])->sum() == 0)
                <p class="text-muted text-center">{{ __('app.invalid_input') }}</p>
            @endif
        </div>
    </div>
</div>

@php
function getBreakdownIcon($key) {
    $icons = [
        'appearance' => 'fas fa-clock',
        'goals' => 'fas fa-futbol',
        'assists' => 'fas fa-hands-helping',
        'clean_sheets' => 'fas fa-shield-alt',
        'saves' => 'fas fa-hand-paper',
        'penalty_saves' => 'fas fa-hand-rock',
        'goals_conceded' => 'fas fa-arrow-down',
        'yellow_cards' => 'fas fa-square text-warning',
        'red_cards' => 'fas fa-square text-danger',
        'own_goals' => 'fas fa-times-circle',
        'penalty_misses' => 'fas fa-times'
    ];
    return $icons[$key] ?? 'fas fa-circle';
}

function getBreakdownLabel($key) {
    $labels = [
        'appearance' => __('app.appearance_points'),
        'goals' => __('app.goal_points'),
        'assists' => __('app.assist_points'),
        'clean_sheets' => __('app.clean_sheet_points'),
        'saves' => __('app.save_points'),
        'penalty_saves' => __('app.penalty_save_points'),
        'goals_conceded' => __('app.goals_conceded'),
        'yellow_cards' => __('app.yellow_card_points'),
        'red_cards' => __('app.red_card_points'),
        'own_goals' => __('app.own_goal_points'),
        'penalty_misses' => __('app.penalty_miss_points')
    ];
    return $labels[$key] ?? $key;
}
@endphp
