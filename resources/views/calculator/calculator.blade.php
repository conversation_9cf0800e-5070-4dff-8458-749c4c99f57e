@extends('layouts.calculator')

@section('title', __('app.points_calculator'))

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h1 class="display-5 fw-bold text-white">
                <i class="fas fa-calculator me-3"></i>
                {{ __('app.points_calculator') }}
            </h1>
        </div>
    </div>
    
    <div class="row">
        <!-- Calculator Form -->
        <div class="col-lg-8">
            <div class="fpl-card">
                <div class="fpl-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        {{ __('app.player_info') }}
                    </h5>
                </div>
                <div class="card-body">
                    <form id="calculatorForm">
                        @csrf
                        
                        <!-- Player Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="player_name" class="form-label fw-bold">
                                    {{ __('app.player_name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="player_name" name="player_name" 
                                       placeholder="{{ __('app.player_name') }}" required
                                       value="{{ old('player_name', $playerData['player_name'] ?? '') }}">
                            </div>
                            <div class="col-md-6">
                                <label for="position" class="form-label fw-bold">
                                    {{ __('app.player_position') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="position" name="position" required>
                                    <option value="">{{ __('app.select_position') }}</option>
                                    <option value="GK" {{ old('position', $playerData['position'] ?? '') == 'GK' ? 'selected' : '' }}>
                                        {{ __('app.goalkeeper') }}
                                    </option>
                                    <option value="DEF" {{ old('position', $playerData['position'] ?? '') == 'DEF' ? 'selected' : '' }}>
                                        {{ __('app.defender') }}
                                    </option>
                                    <option value="MID" {{ old('position', $playerData['position'] ?? '') == 'MID' ? 'selected' : '' }}>
                                        {{ __('app.midfielder') }}
                                    </option>
                                    <option value="FWD" {{ old('position', $playerData['position'] ?? '') == 'FWD' ? 'selected' : '' }}>
                                        {{ __('app.forward') }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Performance Statistics -->
                        <h6 class="fw-bold mb-3">
                            <i class="fas fa-chart-bar me-2"></i>
                            {{ __('app.performance_stats') }}
                        </h6>
                        
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="goals" class="form-label">{{ __('app.goals') }}</label>
                                <input type="number" class="form-control" id="goals" name="goals" min="0" max="50"
                                       value="{{ old('goals', $playerData['goals'] ?? 0) }}">
                            </div>
                            <div class="col-md-4">
                                <label for="assists" class="form-label">{{ __('app.assists') }}</label>
                                <input type="number" class="form-control" id="assists" name="assists" min="0" max="50"
                                       value="{{ old('assists', $playerData['assists'] ?? 0) }}">
                            </div>
                            <div class="col-md-4">
                                <label for="minutes_played" class="form-label">{{ __('app.minutes_played') }}</label>
                                <input type="number" class="form-control" id="minutes_played" name="minutes_played" min="0" max="90"
                                       value="{{ old('minutes_played', $playerData['minutes_played'] ?? 0) }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="clean_sheets" class="form-label">{{ __('app.clean_sheets') }}</label>
                                <input type="number" class="form-control" id="clean_sheets" name="clean_sheets" min="0" max="10"
                                       value="{{ old('clean_sheets', $playerData['clean_sheets'] ?? 0) }}">
                            </div>
                            <div class="col-md-4">
                                <label for="goals_conceded" class="form-label">{{ __('app.goals_conceded') }}</label>
                                <input type="number" class="form-control" id="goals_conceded" name="goals_conceded" min="0" max="50"
                                       value="{{ old('goals_conceded', $playerData['goals_conceded'] ?? 0) }}">
                            </div>
                            <div class="col-md-4">
                                <label for="saves" class="form-label">{{ __('app.saves') }}</label>
                                <input type="number" class="form-control" id="saves" name="saves" min="0" max="50"
                                       value="{{ old('saves', $playerData['saves'] ?? 0) }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="penalty_saves" class="form-label">{{ __('app.penalty_saves') }}</label>
                                <input type="number" class="form-control" id="penalty_saves" name="penalty_saves" min="0" max="10"
                                       value="{{ old('penalty_saves', $playerData['penalty_saves'] ?? 0) }}">
                            </div>
                            <div class="col-md-4">
                                <label for="penalty_misses" class="form-label">{{ __('app.penalty_misses') }}</label>
                                <input type="number" class="form-control" id="penalty_misses" name="penalty_misses" min="0" max="10"
                                       value="{{ old('penalty_misses', $playerData['penalty_misses'] ?? 0) }}">
                            </div>
                            <div class="col-md-4">
                                <label for="yellow_cards" class="form-label">{{ __('app.yellow_cards') }}</label>
                                <input type="number" class="form-control" id="yellow_cards" name="yellow_cards" min="0" max="10"
                                       value="{{ old('yellow_cards', $playerData['yellow_cards'] ?? 0) }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="red_cards" class="form-label">{{ __('app.red_cards') }}</label>
                                <input type="number" class="form-control" id="red_cards" name="red_cards" min="0" max="5"
                                       value="{{ old('red_cards', $playerData['red_cards'] ?? 0) }}">
                            </div>
                            <div class="col-md-4">
                                <label for="own_goals" class="form-label">{{ __('app.own_goals') }}</label>
                                <input type="number" class="form-control" id="own_goals" name="own_goals" min="0" max="10"
                                       value="{{ old('own_goals', $playerData['own_goals'] ?? 0) }}">
                            </div>
                        </div>
                        
                        <!-- حفظ سجل النقاط -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="save_record" name="save_record" value="1">
                                    <label class="form-check-label" for="save_record">
                                        <i class="fas fa-save me-1"></i>
                                        {{ __('app.save_points_record') ?? 'حفظ سجل النقاط' }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-3 justify-content-center">
                                    <button type="submit" class="btn btn-fpl-success btn-lg">
                                        <i class="fas fa-calculator me-2"></i>
                                        {{ __('app.calculate') }}
                                    </button>
                                    <button type="button" class="btn btn-fpl-secondary btn-lg" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        {{ __('app.reset') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Results Panel -->
        <div class="col-lg-4">
            <div class="results-panel" id="resultsPanel">
                @if(isset($result))
                    @include('calculator.partials.results', ['result' => $result])
                @else
                    <div class="fpl-card text-center">
                        <div class="card-body p-5">
                            <i class="fas fa-calculator fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">{{ __('app.results') }}</h5>
                            <p class="text-muted">{{ __('app.enter_player_name') }}</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // تحديث النتائج عند تغيير أي قيمة
    $('#calculatorForm input, #calculatorForm select').on('input change', function() {
        if ($('#player_name').val() && $('#position').val()) {
            calculatePoints();
        }
    });
    
    // إرسال النموذج
    $('#calculatorForm').on('submit', function(e) {
        e.preventDefault();
        calculatePoints();
    });
});

function calculatePoints() {
    const formData = new FormData($('#calculatorForm')[0]);
    
    // إظهار loading
    $('#resultsPanel').html(`
        <div class="fpl-card text-center">
            <div class="card-body p-5">
                <div class="loading-spinner mb-3"></div>
                <h5>{{ __('app.calculate') }}...</h5>
            </div>
        </div>
    `);
    
    $.ajax({
        url: '{{ route("calculator.calculate") }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                displayResults(response.data);
                showAlert(response.message, 'success');
            } else {
                showAlert('{{ __("app.invalid_input") }}', 'danger');
            }
        },
        error: function(xhr) {
            let message = '{{ __("app.invalid_input") }}';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showAlert(message, 'danger');
            
            $('#resultsPanel').html(`
                <div class="fpl-card text-center">
                    <div class="card-body p-5">
                        <i class="fas fa-exclamation-triangle fa-4x text-danger mb-3"></i>
                        <h5 class="text-danger">Error</h5>
                        <p class="text-muted">${message}</p>
                    </div>
                </div>
            `);
        }
    });
}

function displayResults(result) {
    const breakdown = result.breakdown;
    let breakdownHtml = '';
    
    // إنشاء تفصيل النقاط
    Object.keys(breakdown).forEach(function(key) {
        const value = breakdown[key];
        if (value !== 0) {
            const className = value > 0 ? 'positive' : (value < 0 ? 'negative' : 'neutral');
            const icon = getBreakdownIcon(key);
            const label = getBreakdownLabel(key);
            
            breakdownHtml += `
                <div class="breakdown-item ${className}">
                    <span>
                        <i class="${icon} me-2"></i>
                        ${label}
                    </span>
                    <span class="fw-bold">${value > 0 ? '+' : ''}${value}</span>
                </div>
            `;
        }
    });
    
    // إضافة رسالة حفظ السجل إذا تم حفظه
    let recordSavedMessage = '';
    if (result.record_saved) {
        recordSavedMessage = `
            <div class="alert alert-success mt-3 mb-0">
                <i class="fas fa-check-circle me-2"></i>
                {{ __('app.points_record_saved') ?? 'تم حفظ سجل النقاط بنجاح' }}
            </div>
        `;
    }
    
    const resultsHtml = `
        <div class="fpl-card">
            <div class="fpl-card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    {{ __('app.results') }}
                </h5>
            </div>
            <div class="card-body">
                <div class="result-card">
                    <h6 class="mb-2">${result.player_name}</h6>
                    <div class="position-badge">
                        <span class="badge bg-light text-dark">${result.position}</span>
                    </div>
                    <div class="total-points">${result.total_points}</div>
                    <p class="mb-0">{{ __('app.total_points') }}</p>
                </div>
                
                <h6 class="fw-bold mb-3">{{ __('app.points_breakdown') }}</h6>
                <div class="breakdown-list">
                    ${breakdownHtml || '<p class="text-muted text-center">{{ __("app.invalid_input") }}</p>'}
                </div>
                ${recordSavedMessage}
            </div>
        </div>
    `;
    
    $('#resultsPanel').html(resultsHtml);
    
    // إذا تم حفظ السجل، نعرض رسالة نجاح
    if (result.record_saved) {
        showAlert('{{ __('app.points_record_saved') ?? 'تم حفظ سجل النقاط بنجاح' }}', 'success');
    }
}

function getBreakdownIcon(key) {
    const icons = {
        'appearance': 'fas fa-clock',
        'goals': 'fas fa-futbol',
        'assists': 'fas fa-hands-helping',
        'clean_sheets': 'fas fa-shield-alt',
        'saves': 'fas fa-hand-paper',
        'penalty_saves': 'fas fa-hand-rock',
        'goals_conceded': 'fas fa-arrow-down',
        'yellow_cards': 'fas fa-square',
        'red_cards': 'fas fa-square',
        'own_goals': 'fas fa-own-goal',
        'penalty_misses': 'fas fa-times'
    };
    return icons[key] || 'fas fa-circle';
}

function getBreakdownLabel(key) {
    const labels = {
        'appearance': '{{ __("app.appearance_points") }}',
        'goals': '{{ __("app.goal_points") }}',
        'assists': '{{ __("app.assist_points") }}',
        'clean_sheets': '{{ __("app.clean_sheet_points") }}',
        'saves': '{{ __("app.save_points") }}',
        'penalty_saves': '{{ __("app.penalty_save_points") }}',
        'goals_conceded': '{{ __("app.goals_conceded") }}',
        'yellow_cards': '{{ __("app.yellow_card_points") }}',
        'red_cards': '{{ __("app.red_card_points") }}',
        'own_goals': '{{ __("app.own_goal_points") }}',
        'penalty_misses': '{{ __("app.penalty_miss_points") }}'
    };
    return labels[key] || key;
}

function resetForm() {
    $('#calculatorForm')[0].reset();
    $('#resultsPanel').html(`
        <div class="fpl-card text-center">
            <div class="card-body p-5">
                <i class="fas fa-calculator fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">{{ __('app.results') }}</h5>
                <p class="text-muted">{{ __('app.enter_player_name') }}</p>
            </div>
        </div>
    `);
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('.container').first().prepend(alert);
    setTimeout(() => alert.fadeOut(), 5000);
}
</script>
@endpush
@endsection
