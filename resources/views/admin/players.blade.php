@extends('layouts.fpl')

@section('title', 'إدارة اللاعبين - Fantasy Premier League')

@section('content')
<!-- Hero Section -->
<section class="fpl-hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="fpl-hero-title">
                    <i class="fas fa-users-cog me-3"></i>
                    إدارة اللاعبين
                </h1>
                <p class="fpl-hero-subtitle">
                    تحديث إحصائيات اللاعبين وحساب النقاط في الوقت الفعلي
                </p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="fpl-hero-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="totalPlayersCount">
                            <div class="loading-spinner"></div>
                        </div>
                        <div class="stat-label">إجمالي اللاعبين</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-3">
            <label for="teamFilterAdmin" class="form-label fw-bold">الفريق</label>
            <select class="form-select" id="teamFilterAdmin">
                <option value="">جميع الفرق</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="positionFilterAdmin" class="form-label fw-bold">المركز</label>
            <select class="form-select" id="positionFilterAdmin">
                <option value="">جميع المراكز</option>
                <option value="GK">حراس المرمى</option>
                <option value="DEF">المدافعون</option>
                <option value="MID">خط الوسط</option>
                <option value="FWD">المهاجمون</option>
            </select>
        </div>
        <div class="col-md-4">
            <label for="searchPlayerAdmin" class="form-label fw-bold">البحث</label>
            <input type="text" class="form-control" id="searchPlayerAdmin" placeholder="ابحث عن لاعب...">
        </div>
        <div class="col-md-2">
            <label class="form-label fw-bold">&nbsp;</label>
            <div class="d-grid gap-2">
                <button class="btn btn-fpl-primary" onclick="refreshPlayers()">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث
                </button>
                <button class="btn btn-fpl-secondary" onclick="showBulkUpdateModal()">
                    <i class="fas fa-edit me-2"></i>
                    تحديث جماعي
                </button>
            </div>
        </div>
    </div>

    <!-- Players Table -->
    <div class="fpl-card">
        <div class="fpl-card-header">
            <h5 class="mb-0">
                <i class="fas fa-table me-2"></i>
                قائمة اللاعبين
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="playersTable">
                    <thead class="table-dark">
                        <tr>
                            <th>اللاعب</th>
                            <th>الفريق</th>
                            <th>المركز</th>
                            <th>السعر</th>
                            <th>النقاط</th>
                            <th>الأهداف</th>
                            <th>التمريرات</th>
                            <th>الدقائق</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="playersTableBody">
                        <tr>
                            <td colspan="9" class="text-center py-5">
                                <div class="loading-spinner mb-3"></div>
                                <h5>جاري تحميل اللاعبين...</h5>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Edit Player Modal -->
<div class="modal fade" id="editPlayerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تحديث إحصائيات اللاعب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editPlayerForm">
                    <input type="hidden" id="editPlayerId">
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 id="editPlayerName" class="fw-bold"></h6>
                            <p class="text-muted mb-0" id="editPlayerTeam"></p>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-primary fs-6" id="editPlayerPosition"></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editGoals" class="form-label">الأهداف</label>
                            <input type="number" class="form-control" id="editGoals" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editAssists" class="form-label">التمريرات الحاسمة</label>
                            <input type="number" class="form-control" id="editAssists" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editCleanSheets" class="form-label">الشباك النظيفة</label>
                            <input type="number" class="form-control" id="editCleanSheets" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editGoalsConceded" class="form-label">الأهداف المستقبلة</label>
                            <input type="number" class="form-control" id="editGoalsConceded" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editSaves" class="form-label">التصديات</label>
                            <input type="number" class="form-control" id="editSaves" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPenaltySaves" class="form-label">تصدي ضربات جزاء</label>
                            <input type="number" class="form-control" id="editPenaltySaves" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPenaltyMisses" class="form-label">ضربات جزاء ضائعة</label>
                            <input type="number" class="form-control" id="editPenaltyMisses" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editYellowCards" class="form-label">البطاقات الصفراء</label>
                            <input type="number" class="form-control" id="editYellowCards" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editRedCards" class="form-label">البطاقات الحمراء</label>
                            <input type="number" class="form-control" id="editRedCards" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editOwnGoals" class="form-label">الأهداف في المرمى</label>
                            <input type="number" class="form-control" id="editOwnGoals" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editMinutesPlayed" class="form-label">الدقائق المُلعبة</label>
                            <input type="number" class="form-control" id="editMinutesPlayed" min="0" max="90">
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>النقاط المحسوبة: </strong>
                        <span id="calculatedPoints" class="fw-bold">0</span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-fpl-primary" onclick="savePlayerStats()">
                    <i class="fas fa-save me-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Update Modal -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-users-cog me-2"></i>
                    التحديث الجماعي للاعبين
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    يمكنك تحديث إحصائيات عدة لاعبين في نفس الوقت. اتركي الحقول فارغة إذا لم تريدي تغييرها.
                </div>

                <form id="bulkUpdateForm">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="bulkTeamFilter" class="form-label">الفريق</label>
                            <select class="form-select" id="bulkTeamFilter">
                                <option value="">جميع الفرق</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="bulkPositionFilter" class="form-label">المركز</label>
                            <select class="form-select" id="bulkPositionFilter">
                                <option value="">جميع المراكز</option>
                                <option value="GK">حراس المرمى</option>
                                <option value="DEF">المدافعون</option>
                                <option value="MID">خط الوسط</option>
                                <option value="FWD">المهاجمون</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-outline-primary w-100" onclick="loadBulkPlayers()">
                                <i class="fas fa-search me-2"></i>
                                تحميل اللاعبين
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>القيم المراد تحديثها:</h6>
                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <label for="bulkGoals" class="form-label">الأهداف</label>
                                    <input type="number" class="form-control" id="bulkGoals" min="0" placeholder="اتركه فارغاً">
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label for="bulkAssists" class="form-label">التمريرات</label>
                                    <input type="number" class="form-control" id="bulkAssists" min="0" placeholder="اتركه فارغاً">
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label for="bulkMinutes" class="form-label">الدقائق</label>
                                    <input type="number" class="form-control" id="bulkMinutes" min="0" max="90" placeholder="اتركه فارغاً">
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label for="bulkYellowCards" class="form-label">البطاقات الصفراء</label>
                                    <input type="number" class="form-control" id="bulkYellowCards" min="0" placeholder="اتركه فارغاً">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>اللاعبون المحددون:</h6>
                            <div id="selectedPlayersList" class="border rounded p-3" style="height: 200px; overflow-y: auto;">
                                <p class="text-muted text-center">اختر الفريق والمركز أولاً</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-fpl-primary" onclick="saveBulkUpdate()" disabled id="saveBulkBtn">
                    <i class="fas fa-save me-2"></i>
                    حفظ التحديثات
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    loadPlayers();
    loadTeamsFilter();

    // تسجيل callback للتحديثات في الوقت الفعلي
    if (window.realTimeUpdates) {
        window.realTimeUpdates.onPlayerUpdate(function(players) {
            displayPlayersTable(players);
            $('#totalPlayersCount').text(players.length);
        });
    }

    // البحث والفلترة
    $('#teamFilterAdmin, #positionFilterAdmin').change(function() {
        loadPlayers();
    });

    $('#searchPlayerAdmin').on('input', debounce(function() {
        loadPlayers();
    }, 500));

    // حساب النقاط عند تغيير القيم
    $('#editPlayerForm input').on('input', calculatePreviewPoints);
});

function loadPlayers() {
    const filters = {
        team_id: $('#teamFilterAdmin').val(),
        position: $('#positionFilterAdmin').val(),
        search: $('#searchPlayerAdmin').val()
    };
    
    $.ajax({
        url: '{{ route("api.players") }}',
        method: 'GET',
        data: filters,
        success: function(players) {
            displayPlayersTable(players);
            $('#totalPlayersCount').text(players.length);
        },
        error: function() {
            $('#playersTableBody').html(`
                <tr>
                    <td colspan="9" class="text-center py-4 text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <br>حدث خطأ في تحميل البيانات
                    </td>
                </tr>
            `);
        }
    });
}

function displayPlayersTable(players) {
    let tableHtml = '';
    
    if (players.length === 0) {
        tableHtml = `
            <tr>
                <td colspan="9" class="text-center py-4">
                    <i class="fas fa-search fa-2x mb-2 text-muted"></i>
                    <br>لا توجد نتائج
                </td>
            </tr>
        `;
    } else {
        players.forEach(function(player) {
            tableHtml += `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div>
                                <h6 class="mb-0">${player.name}</h6>
                            </div>
                        </div>
                    </td>
                    <td>${player.team}</td>
                    <td>
                        <span class="badge bg-primary">${player.position}</span>
                    </td>
                    <td class="fw-bold text-success">${player.price}م</td>
                    <td>
                        <span class="badge bg-info fs-6">${player.points}</span>
                    </td>
                    <td>${player.goals}</td>
                    <td>${player.assists}</td>
                    <td>${player.minutes_played}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editPlayer(${player.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#playersTableBody').html(tableHtml);
}

function loadTeamsFilter() {
    $.ajax({
        url: '{{ route("api.teams") }}',
        method: 'GET',
        success: function(teams) {
            let options = '<option value="">جميع الفرق</option>';
            teams.forEach(function(team) {
                options += `<option value="${team.id}">${team.name}</option>`;
            });
            $('#teamFilterAdmin').html(options);
        }
    });
}

function editPlayer(playerId) {
    // البحث عن اللاعب في البيانات المحملة
    $.ajax({
        url: '{{ route("api.players") }}',
        method: 'GET',
        data: { player_id: playerId },
        success: function(players) {
            const player = players.find(p => p.id == playerId);
            if (player) {
                populateEditForm(player);
                $('#editPlayerModal').modal('show');
            }
        }
    });
}

function populateEditForm(player) {
    $('#editPlayerId').val(player.id);
    $('#editPlayerName').text(player.name);
    $('#editPlayerTeam').text(player.team);
    $('#editPlayerPosition').text(player.position);
    
    $('#editGoals').val(player.goals || 0);
    $('#editAssists').val(player.assists || 0);
    $('#editCleanSheets').val(player.clean_sheets || 0);
    $('#editGoalsConceded').val(player.goals_conceded || 0);
    $('#editSaves').val(player.saves || 0);
    $('#editPenaltySaves').val(player.penalty_saves || 0);
    $('#editPenaltyMisses').val(player.penalty_misses || 0);
    $('#editYellowCards').val(player.yellow_cards || 0);
    $('#editRedCards').val(player.red_cards || 0);
    $('#editOwnGoals').val(player.own_goals || 0);
    $('#editMinutesPlayed').val(player.minutes_played || 0);
    
    calculatePreviewPoints();
}

function calculatePreviewPoints() {
    // حساب النقاط حسب القوانين
    const goals = parseInt($('#editGoals').val()) || 0;
    const assists = parseInt($('#editAssists').val()) || 0;
    const cleanSheets = parseInt($('#editCleanSheets').val()) || 0;
    const saves = parseInt($('#editSaves').val()) || 0;
    const penaltySaves = parseInt($('#editPenaltySaves').val()) || 0;
    const yellowCards = parseInt($('#editYellowCards').val()) || 0;
    const redCards = parseInt($('#editRedCards').val()) || 0;
    const ownGoals = parseInt($('#editOwnGoals').val()) || 0;
    const minutesPlayed = parseInt($('#editMinutesPlayed').val()) || 0;
    
    let points = 0;
    
    // نقاط الأهداف والتمريرات
    points += goals * 4 + assists * 3;
    
    // نقاط الشباك النظيفة
    points += cleanSheets * 4;
    
    // نقاط التصديات
    points += Math.floor(saves / 3) * 1;
    
    // نقاط تصدي ضربات الجزاء
    points += penaltySaves * 5;
    
    // خصم نقاط البطاقات والأهداف في المرمى
    points -= yellowCards * 1;
    points -= redCards * 3;
    points -= ownGoals * 2;
    
    // نقاط المشاركة
    if (minutesPlayed >= 60) {
        points += 2;
    } else if (minutesPlayed > 0) {
        points += 1;
    }
    
    $('#calculatedPoints').text(points);
}

function savePlayerStats() {
    const playerId = $('#editPlayerId').val();
    const formData = {
        goals: parseInt($('#editGoals').val()) || 0,
        assists: parseInt($('#editAssists').val()) || 0,
        clean_sheets: parseInt($('#editCleanSheets').val()) || 0,
        goals_conceded: parseInt($('#editGoalsConceded').val()) || 0,
        saves: parseInt($('#editSaves').val()) || 0,
        penalty_saves: parseInt($('#editPenaltySaves').val()) || 0,
        penalty_misses: parseInt($('#editPenaltyMisses').val()) || 0,
        yellow_cards: parseInt($('#editYellowCards').val()) || 0,
        red_cards: parseInt($('#editRedCards').val()) || 0,
        own_goals: parseInt($('#editOwnGoals').val()) || 0,
        minutes_played: parseInt($('#editMinutesPlayed').val()) || 0
    };
    
    $.ajax({
        url: `/api/player/${playerId}/stats`,
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                $('#editPlayerModal').modal('hide');
                loadPlayers(); // إعادة تحميل الجدول
                
                // إشعار بتحديث النقاط
                broadcastPointsUpdate(playerId, response.player.points);
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function(xhr) {
            let message = 'حدث خطأ أثناء حفظ البيانات';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showAlert(message, 'danger');
        }
    });
}

function broadcastPointsUpdate(playerId, newPoints) {
    // إشعار جميع المستخدمين بتحديث النقاط
    // يمكن استخدام WebSockets أو Server-Sent Events هنا
    console.log(`Player ${playerId} points updated to ${newPoints}`);
}

function refreshPlayers() {
    loadPlayers();
    showAlert('تم تحديث البيانات', 'success');
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showBulkUpdateModal() {
    // تحميل قائمة الفرق في modal
    $.ajax({
        url: '{{ route("api.teams") }}',
        method: 'GET',
        success: function(teams) {
            let options = '<option value="">جميع الفرق</option>';
            teams.forEach(function(team) {
                options += `<option value="${team.id}">${team.name}</option>`;
            });
            $('#bulkTeamFilter').html(options);
        }
    });

    $('#bulkUpdateModal').modal('show');
}

function loadBulkPlayers() {
    const teamId = $('#bulkTeamFilter').val();
    const position = $('#bulkPositionFilter').val();

    if (!teamId && !position) {
        showAlert('يرجى اختيار الفريق أو المركز على الأقل', 'danger');
        return;
    }

    const filters = {
        team_id: teamId,
        position: position
    };

    $.ajax({
        url: '{{ route("api.players") }}',
        method: 'GET',
        data: filters,
        success: function(players) {
            displayBulkPlayersList(players);
        },
        error: function() {
            $('#selectedPlayersList').html('<p class="text-danger text-center">حدث خطأ في تحميل اللاعبين</p>');
        }
    });
}

function displayBulkPlayersList(players) {
    if (players.length === 0) {
        $('#selectedPlayersList').html('<p class="text-muted text-center">لا توجد لاعبين مطابقين للفلتر</p>');
        $('#saveBulkBtn').prop('disabled', true);
        return;
    }

    let playersHtml = '';
    players.forEach(function(player) {
        playersHtml += `
            <div class="form-check mb-2">
                <input class="form-check-input bulk-player-checkbox" type="checkbox" value="${player.id}" id="player_${player.id}">
                <label class="form-check-label" for="player_${player.id}">
                    <strong>${player.name}</strong> - ${player.team} (${player.position})
                </label>
            </div>
        `;
    });

    $('#selectedPlayersList').html(playersHtml);
    $('#saveBulkBtn').prop('disabled', false);

    // تفعيل/إلغاء تفعيل زر الحفظ حسب الاختيار
    $('.bulk-player-checkbox').change(function() {
        const checkedCount = $('.bulk-player-checkbox:checked').length;
        $('#saveBulkBtn').prop('disabled', checkedCount === 0);
    });
}

function saveBulkUpdate() {
    const selectedPlayers = $('.bulk-player-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedPlayers.length === 0) {
        showAlert('يرجى اختيار لاعب واحد على الأقل', 'danger');
        return;
    }

    const updateData = {};

    // جمع البيانات المراد تحديثها
    if ($('#bulkGoals').val()) updateData.goals = parseInt($('#bulkGoals').val());
    if ($('#bulkAssists').val()) updateData.assists = parseInt($('#bulkAssists').val());
    if ($('#bulkMinutes').val()) updateData.minutes_played = parseInt($('#bulkMinutes').val());
    if ($('#bulkYellowCards').val()) updateData.yellow_cards = parseInt($('#bulkYellowCards').val());

    if (Object.keys(updateData).length === 0) {
        showAlert('يرجى إدخال قيمة واحدة على الأقل للتحديث', 'danger');
        return;
    }

    // تحديث كل لاعب محدد
    let completedUpdates = 0;
    let totalUpdates = selectedPlayers.length;
    let successCount = 0;

    $('#saveBulkBtn').prop('disabled', true).html('<div class="loading-spinner me-2"></div>جاري الحفظ...');

    selectedPlayers.forEach(function(playerId) {
        $.ajax({
            url: `/api/player/${playerId}/stats`,
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(updateData),
            success: function(response) {
                if (response.success) {
                    successCount++;
                }
                completedUpdates++;
                checkBulkUpdateCompletion(completedUpdates, totalUpdates, successCount);
            },
            error: function() {
                completedUpdates++;
                checkBulkUpdateCompletion(completedUpdates, totalUpdates, successCount);
            }
        });
    });
}

function checkBulkUpdateCompletion(completed, total, success) {
    if (completed === total) {
        $('#saveBulkBtn').prop('disabled', false).html('<i class="fas fa-save me-2"></i>حفظ التحديثات');

        if (success === total) {
            showAlert(`تم تحديث ${success} لاعب بنجاح`, 'success');
        } else {
            showAlert(`تم تحديث ${success} من ${total} لاعب`, 'warning');
        }

        $('#bulkUpdateModal').modal('hide');
        loadPlayers(); // إعادة تحميل الجدول

        // مسح النموذج
        $('#bulkUpdateForm')[0].reset();
        $('#selectedPlayersList').html('<p class="text-muted text-center">اختر الفريق والمركز أولاً</p>');
    }
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-fpl-success' : 'alert-fpl-danger';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('.container').first().prepend(alert);
    setTimeout(() => alert.fadeOut(), 5000);
}
</script>
@endpush
@endsection
