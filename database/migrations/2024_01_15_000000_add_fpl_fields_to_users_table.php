<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('fpl_manager_id')->nullable()->after('email');
            $table->string('fpl_team_name')->nullable()->after('fpl_manager_id');
            $table->string('fpl_manager_name')->nullable()->after('fpl_team_name');
            $table->integer('fpl_total_points')->default(0)->after('fpl_manager_name');
            $table->integer('fpl_overall_rank')->nullable()->after('fpl_total_points');
            $table->integer('fpl_gameweek_points')->default(0)->after('fpl_overall_rank');
            $table->decimal('fpl_team_value', 8, 1)->default(100.0)->after('fpl_gameweek_points');
            $table->decimal('fpl_bank', 8, 1)->default(0.0)->after('fpl_team_value');
            $table->json('fpl_data')->nullable()->after('fpl_bank');
            $table->timestamp('fpl_last_updated')->nullable()->after('fpl_data');
            $table->boolean('fpl_connected')->default(false)->after('fpl_last_updated');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'fpl_manager_id',
                'fpl_team_name',
                'fpl_manager_name',
                'fpl_total_points',
                'fpl_overall_rank',
                'fpl_gameweek_points',
                'fpl_team_value',
                'fpl_bank',
                'fpl_data',
                'fpl_last_updated',
                'fpl_connected'
            ]);
        });
    }
};
