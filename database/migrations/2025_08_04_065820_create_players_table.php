<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('players', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('position'); // GK, DEF, MID, FWD
            $table->foreignId('team_id')->constrained()->onDelete('cascade');
            $table->decimal('price', 4, 1)->default(4.0); // سعر اللاعب بالمليون
            $table->integer('goals')->default(0);
            $table->integer('assists')->default(0);
            $table->integer('clean_sheets')->default(0);
            $table->integer('goals_conceded')->default(0);
            $table->integer('saves')->default(0);
            $table->integer('penalty_saves')->default(0);
            $table->integer('penalty_misses')->default(0);
            $table->integer('yellow_cards')->default(0);
            $table->integer('red_cards')->default(0);
            $table->integer('own_goals')->default(0);
            $table->integer('minutes_played')->default(0);
            $table->boolean('is_captain')->default(false);
            $table->boolean('is_vice_captain')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('players');
    }
};
