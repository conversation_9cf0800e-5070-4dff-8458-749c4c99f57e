<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Theme Colors
        $this->createSetting('auth_primary_color', 'var(--fpl-purple)', 'theme', 'string', 'Primary color for authentication pages', true);
        $this->createSetting('auth_secondary_color', 'var(--fpl-green)', 'theme', 'string', 'Secondary color for authentication pages', true);
        $this->createSetting('auth_gradient_start', '#37003c', 'theme', 'string', 'Gradient start color for authentication pages', true);
        $this->createSetting('auth_gradient_end', '#4a0e4e', 'theme', 'string', 'Gradient end color for authentication pages', true);
        $this->createSetting('auth_text_color', '#ffffff', 'theme', 'string', 'Text color for authentication pages', true);
        $this->createSetting('auth_accent_color', '#00ff87', 'theme', 'string', 'Accent color for authentication pages', true);
        
        // Site Settings
        $this->createSetting('site_name', 'Fantasy Premier League', 'general', 'string', 'Site name', true);
        $this->createSetting('site_description', 'Fantasy Premier League Statistics and Analysis', 'general', 'string', 'Site description', true);
    }
    
    /**
     * Create a setting if it doesn't exist
     */
    private function createSetting(string $key, $value, string $group, string $type, string $description, bool $isPublic): void
    {
        if (!Setting::where('key', $key)->exists()) {
            Setting::create([
                'key' => $key,
                'value' => $value,
                'group' => $group,
                'type' => $type,
                'description' => $description,
                'is_public' => $isPublic
            ]);
        }
    }
}