<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Team;

class TeamsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teams = [
            ['name' => 'Arsenal', 'short_name' => 'ARS', 'color_primary' => '#EF0107', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Aston Villa', 'short_name' => 'AVL', 'color_primary' => '#95BFE5', 'color_secondary' => '#670E36'],
            ['name' => 'Brighton & Hove Albion', 'short_name' => 'BHA', 'color_primary' => '#0057B8', 'color_secondary' => '#FFCD00'],
            ['name' => 'Burnley', 'short_name' => 'BUR', 'color_primary' => '#6C1D45', 'color_secondary' => '#99D6EA'],
            ['name' => 'Chelsea', 'short_name' => 'CHE', 'color_primary' => '#034694', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Crystal Palace', 'short_name' => 'CRY', 'color_primary' => '#1B458F', 'color_secondary' => '#A7A5A6'],
            ['name' => 'Everton', 'short_name' => 'EVE', 'color_primary' => '#003399', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Fulham', 'short_name' => 'FUL', 'color_primary' => '#FFFFFF', 'color_secondary' => '#000000'],
            ['name' => 'Liverpool', 'short_name' => 'LIV', 'color_primary' => '#C8102E', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Luton Town', 'short_name' => 'LUT', 'color_primary' => '#F78F1E', 'color_secondary' => '#002D62'],
            ['name' => 'Manchester City', 'short_name' => 'MCI', 'color_primary' => '#6CABDD', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Manchester United', 'short_name' => 'MUN', 'color_primary' => '#DA020E', 'color_secondary' => '#FBE122'],
            ['name' => 'Newcastle United', 'short_name' => 'NEW', 'color_primary' => '#241F20', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Nottingham Forest', 'short_name' => 'NFO', 'color_primary' => '#DD0000', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Sheffield United', 'short_name' => 'SHU', 'color_primary' => '#EE2737', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Tottenham Hotspur', 'short_name' => 'TOT', 'color_primary' => '#132257', 'color_secondary' => '#FFFFFF'],
            ['name' => 'West Ham United', 'short_name' => 'WHU', 'color_primary' => '#7A263A', 'color_secondary' => '#1BB1E7'],
            ['name' => 'Wolverhampton Wanderers', 'short_name' => 'WOL', 'color_primary' => '#FDB913', 'color_secondary' => '#231F20'],
            ['name' => 'Brentford', 'short_name' => 'BRE', 'color_primary' => '#E30613', 'color_secondary' => '#FFFFFF'],
            ['name' => 'Leicester City', 'short_name' => 'LEI', 'color_primary' => '#003090', 'color_secondary' => '#FDBE11'],
        ];

        foreach ($teams as $team) {
            Team::create($team);
        }
    }
}
