<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Player;
use App\Models\Team;

class PlayersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // الحصول على بعض الفرق
        $arsenal = Team::where('short_name', 'ARS')->first();
        $chelsea = Team::where('short_name', 'CHE')->first();
        $liverpool = Team::where('short_name', 'LIV')->first();
        $manCity = Team::where('short_name', 'MCI')->first();
        $manUtd = Team::where('short_name', 'MUN')->first();

        $players = [
            // Arsenal
            ['name' => '<PERSON>', 'position' => 'GK', 'team_id' => $arsenal->id, 'price' => 5.0],
            ['name' => '<PERSON>', 'position' => 'DEF', 'team_id' => $arsenal->id, 'price' => 5.5],
            ['name' => '<PERSON>', 'position' => 'DEF', 'team_id' => $arsenal->id, 'price' => 5.0],
            ['name' => 'Ben White', 'position' => 'DEF', 'team_id' => $arsenal->id, 'price' => 4.5],
            ['name' => 'Oleksandr Zinchenko', 'position' => 'DEF', 'team_id' => $arsenal->id, 'price' => 5.0],
            ['name' => 'Thomas Partey', 'position' => 'MID', 'team_id' => $arsenal->id, 'price' => 5.0],
            ['name' => 'Martin Ødegaard', 'position' => 'MID', 'team_id' => $arsenal->id, 'price' => 8.5],
            ['name' => 'Bukayo Saka', 'position' => 'MID', 'team_id' => $arsenal->id, 'price' => 8.0],
            ['name' => 'Gabriel Martinelli', 'position' => 'FWD', 'team_id' => $arsenal->id, 'price' => 7.0],
            ['name' => 'Gabriel Jesus', 'position' => 'FWD', 'team_id' => $arsenal->id, 'price' => 8.0],

            // Chelsea
            ['name' => 'Robert Sánchez', 'position' => 'GK', 'team_id' => $chelsea->id, 'price' => 4.5],
            ['name' => 'Thiago Silva', 'position' => 'DEF', 'team_id' => $chelsea->id, 'price' => 5.0],
            ['name' => 'Reece James', 'position' => 'DEF', 'team_id' => $chelsea->id, 'price' => 6.0],
            ['name' => 'Ben Chilwell', 'position' => 'DEF', 'team_id' => $chelsea->id, 'price' => 5.5],
            ['name' => 'Enzo Fernández', 'position' => 'MID', 'team_id' => $chelsea->id, 'price' => 8.0],
            ['name' => 'Mason Mount', 'position' => 'MID', 'team_id' => $chelsea->id, 'price' => 7.5],
            ['name' => 'Raheem Sterling', 'position' => 'MID', 'team_id' => $chelsea->id, 'price' => 10.0],
            ['name' => 'Christopher Nkunku', 'position' => 'FWD', 'team_id' => $chelsea->id, 'price' => 7.5],
            ['name' => 'Nicolas Jackson', 'position' => 'FWD', 'team_id' => $chelsea->id, 'price' => 7.0],

            // Liverpool
            ['name' => 'Alisson Becker', 'position' => 'GK', 'team_id' => $liverpool->id, 'price' => 5.5],
            ['name' => 'Virgil van Dijk', 'position' => 'DEF', 'team_id' => $liverpool->id, 'price' => 6.5],
            ['name' => 'Andrew Robertson', 'position' => 'DEF', 'team_id' => $liverpool->id, 'price' => 6.0],
            ['name' => 'Trent Alexander-Arnold', 'position' => 'DEF', 'team_id' => $liverpool->id, 'price' => 7.5],
            ['name' => 'Jordan Henderson', 'position' => 'MID', 'team_id' => $liverpool->id, 'price' => 5.0],
            ['name' => 'Mohamed Salah', 'position' => 'MID', 'team_id' => $liverpool->id, 'price' => 13.0],
            ['name' => 'Sadio Mané', 'position' => 'FWD', 'team_id' => $liverpool->id, 'price' => 12.0],
            ['name' => 'Darwin Núñez', 'position' => 'FWD', 'team_id' => $liverpool->id, 'price' => 9.0],

            // Manchester City
            ['name' => 'Ederson', 'position' => 'GK', 'team_id' => $manCity->id, 'price' => 5.5],
            ['name' => 'Rúben Dias', 'position' => 'DEF', 'team_id' => $manCity->id, 'price' => 6.0],
            ['name' => 'João Cancelo', 'position' => 'DEF', 'team_id' => $manCity->id, 'price' => 7.0],
            ['name' => 'Kyle Walker', 'position' => 'DEF', 'team_id' => $manCity->id, 'price' => 5.5],
            ['name' => 'Kevin De Bruyne', 'position' => 'MID', 'team_id' => $manCity->id, 'price' => 12.5],
            ['name' => 'Bernardo Silva', 'position' => 'MID', 'team_id' => $manCity->id, 'price' => 7.0],
            ['name' => 'Erling Haaland', 'position' => 'FWD', 'team_id' => $manCity->id, 'price' => 14.0],
            ['name' => 'Phil Foden', 'position' => 'MID', 'team_id' => $manCity->id, 'price' => 8.0],

            // Manchester United
            ['name' => 'André Onana', 'position' => 'GK', 'team_id' => $manUtd->id, 'price' => 5.0],
            ['name' => 'Harry Maguire', 'position' => 'DEF', 'team_id' => $manUtd->id, 'price' => 4.5],
            ['name' => 'Luke Shaw', 'position' => 'DEF', 'team_id' => $manUtd->id, 'price' => 5.0],
            ['name' => 'Bruno Fernandes', 'position' => 'MID', 'team_id' => $manUtd->id, 'price' => 8.5],
            ['name' => 'Marcus Rashford', 'position' => 'FWD', 'team_id' => $manUtd->id, 'price' => 9.5],
            ['name' => 'Antony', 'position' => 'MID', 'team_id' => $manUtd->id, 'price' => 7.0],
        ];

        foreach ($players as $player) {
            Player::create($player);
        }
    }
}
