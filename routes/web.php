<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\FPLController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\CalculatorController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;

// Main Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::post('/register', [HomeController::class, 'register'])->name('register');
Route::post('/set-language', [HomeController::class, 'setLanguage'])->name('set.language');

// API Routes for News
Route::prefix('api')->group(function () {
    Route::get('/news', [NewsController::class, 'getLatestNews'])->name('api.news');
    Route::get('/news/transfers', [NewsController::class, 'getTransferNews'])->name('api.news.transfers');
    Route::get('/trending', [NewsController::class, 'getTrendingTopics'])->name('api.trending');
});

// Calculator Routes
Route::prefix('calculator')->name('calculator.')->group(function () {
    Route::get('/', [CalculatorController::class, 'index'])->name('index');
    Route::get('/calculator', [CalculatorController::class, 'calculator'])->name('calculator');
    Route::get('/points-calculator', [CalculatorController::class, 'pointsCalculator'])->name('points-calculator');
    Route::post('/calculate', [CalculatorController::class, 'calculate'])->name('calculate');
    Route::post('/calculate-points', [CalculatorController::class, 'calculatePoints'])->name('calculate-points');
    Route::post('/set-language', [CalculatorController::class, 'setLanguage'])->name('setLanguage');
});

// Authentication Routes (if needed for future features)
Route::get('/dashboard', function () {
    return redirect()->route('calculator.index');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    
    // Personal Profile Route
    Route::get('/personal-profile', [\App\Http\Controllers\PersonalProfileController::class, 'index'])->name('profile.personal');

    // FPL Routes
    Route::prefix('fpl')->name('fpl.')->group(function () {
        Route::get('/connect', [FPLController::class, 'connect'])->name('connect');
        Route::post('/connect', [FPLController::class, 'store'])->name('store');
        Route::get('/profile', [FPLController::class, 'profile'])->name('profile');
        Route::post('/update', [FPLController::class, 'update'])->name('update');
        Route::delete('/disconnect', [FPLController::class, 'disconnect'])->name('disconnect');
        Route::get('/team-details', [FPLController::class, 'teamDetails'])->name('team-details');
    });
});

require __DIR__.'/auth.php';
