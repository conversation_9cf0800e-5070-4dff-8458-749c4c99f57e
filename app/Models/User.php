<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'fpl_manager_id',
        'fpl_team_name',
        'fpl_manager_name',
        'fpl_total_points',
        'fpl_overall_rank',
        'fpl_gameweek_points',
        'fpl_team_value',
        'fpl_bank',
        'fpl_data',
        'fpl_last_updated',
        'fpl_connected',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'fpl_data' => 'array',
            'fpl_last_updated' => 'datetime',
            'fpl_connected' => 'boolean',
            'fpl_team_value' => 'decimal:1',
            'fpl_bank' => 'decimal:1',
        ];
    }

    public function fantasyTeams()
    {
        return $this->hasMany(\App\Models\FantasyTeam::class);
    }

    /**
     * Check if user has connected FPL account
     */
    public function hasFPLAccount()
    {
        return $this->fpl_connected && !empty($this->fpl_manager_id);
    }

    /**
     * Get FPL data as array
     */
    public function getFPLData()
    {
        return $this->fpl_data ?? [];
    }

    /**
     * Get formatted team value
     */
    public function getFormattedTeamValue()
    {
        return '£' . number_format($this->fpl_team_value, 1) . 'm';
    }

    /**
     * Get formatted bank value
     */
    public function getFormattedBank()
    {
        return '£' . number_format($this->fpl_bank, 1) . 'm';
    }

    /**
     * Get formatted overall rank
     */
    public function getFormattedOverallRank()
    {
        if (!$this->fpl_overall_rank) return 'N/A';
        return number_format($this->fpl_overall_rank);
    }

    /**
     * Check if FPL data needs update (older than 1 hour)
     */
    public function needsFPLUpdate()
    {
        if (!$this->fpl_last_updated) return true;
        return $this->fpl_last_updated->diffInHours(now()) >= 1;
    }
}
