<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class FantasyTeam extends Model
{
    protected $fillable = [
        'name',
        'manager_name',
        'total_value',
        'total_points',
        'gameweek_points',
        'formation',
        'user_id'
    ];

    protected $casts = [
        'total_value' => 'decimal:1',
        'formation' => 'array'
    ];

    public function players(): BelongsToMany
    {
        return $this->belongsToMany(Player::class)
            ->withPivot(['is_captain', 'is_vice_captain', 'is_playing', 'position_order'])
            ->withTimestamps();
    }

    public function playingPlayers(): BelongsToMany
    {
        return $this->players()->wherePivot('is_playing', true);
    }

    public function benchPlayers(): BelongsToMany
    {
        return $this->players()->wherePivot('is_playing', false);
    }

    public function captain()
    {
        return $this->players()->wherePivot('is_captain', true)->first();
    }

    public function viceCaptain()
    {
        return $this->players()->wherePivot('is_vice_captain', true)->first();
    }

    /**
     * حساب النقاط الإجمالية للفريق
     */
    public function calculateTotalPoints(): int
    {
        $totalPoints = 0;

        foreach ($this->playingPlayers as $player) {
            $playerPoints = $player->calculatePoints();

            // مضاعفة نقاط الكابتن
            if ($player->pivot->is_captain) {
                $playerPoints *= 2;
            }

            $totalPoints += $playerPoints;
        }

        return $totalPoints;
    }

    /**
     * التحقق من صحة التشكيلة
     */
    public function isValidFormation(): bool
    {
        $playingPlayers = $this->playingPlayers;

        if ($playingPlayers->count() !== 11) {
            return false;
        }

        $positions = $playingPlayers->groupBy('position');

        // التحقق من عدد اللاعبين في كل مركز
        if (
            ($positions->get('GK', collect())->count() !== 1) ||
            ($positions->get('DEF', collect())->count() < 3 || $positions->get('DEF', collect())->count() > 5) ||
            ($positions->get('MID', collect())->count() < 3 || $positions->get('MID', collect())->count() > 5) ||
            ($positions->get('FWD', collect())->count() < 1 || $positions->get('FWD', collect())->count() > 3)
        ) {
            return false;
        }

        // التحقق من عدم وجود أكثر من 3 لاعبين من نفس الفريق
        $teamCounts = $playingPlayers->groupBy('team_id');
        foreach ($teamCounts as $teamPlayers) {
            if ($teamPlayers->count() > 3) {
                return false;
            }
        }

        return true;
    }

    /**
     * حساب القيمة الإجمالية للفريق
     */
    public function calculateTotalValue(): float
    {
        return $this->players->sum('price');
    }

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }
}
