<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Player extends Model
{
    protected $fillable = [
        'name',
        'position',
        'team_id',
        'price',
        'goals',
        'assists',
        'clean_sheets',
        'goals_conceded',
        'saves',
        'penalty_saves',
        'penalty_misses',
        'yellow_cards',
        'red_cards',
        'own_goals',
        'minutes_played',
        'is_captain',
        'is_vice_captain'
    ];

    protected $casts = [
        'price' => 'decimal:1',
        'is_captain' => 'boolean',
        'is_vice_captain' => 'boolean'
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function fantasyTeams(): BelongsToMany
    {
        return $this->belongsToMany(FantasyTeam::class)
            ->withPivot(['is_captain', 'is_vice_captain', 'is_playing', 'position_order'])
            ->withTimestamps();
    }

    /**
     * حساب النقاط الأساسية للاعب
     */
    public function calculatePoints(): int
    {
        $points = 0;

        // نقاط الأهداف حسب المركز
        switch ($this->position) {
            case 'GK':
            case 'DEF':
                $points += $this->goals * 6; // 6 نقاط لكل هدف للحراس والمدافعين
                break;
            case 'MID':
                $points += $this->goals * 5; // 5 نقاط لكل هدف لخط الوسط
                break;
            case 'FWD':
                $points += $this->goals * 4; // 4 نقاط لكل هدف للمهاجمين
                break;
        }

        // نقاط التمريرات الحاسمة
        $points += $this->assists * 3;

        // نقاط الشباك النظيفة (للحراس والمدافعين فقط)
        if (in_array($this->position, ['GK', 'DEF'])) {
            $points += $this->clean_sheets * 4;
        }

        // نقاط الإنقاذات (للحراس فقط)
        if ($this->position === 'GK') {
            $points += floor($this->saves / 3); // نقطة واحدة لكل 3 إنقاذات
            $points += $this->penalty_saves * 5; // 5 نقاط لكل ركلة جزاء محبطة
        }

        // خصم نقاط
        $points -= $this->yellow_cards * 1; // خصم نقطة لكل بطاقة صفراء
        $points -= $this->red_cards * 3; // خصم 3 نقاط لكل بطاقة حمراء
        $points -= $this->own_goals * 2; // خصم نقطتين لكل هدف في المرمى
        $points -= $this->penalty_misses * 2; // خصم نقطتين لكل ركلة جزاء ضائعة

        // خصم نقاط الأهداف المستقبلة (للحراس والمدافعين)
        if (in_array($this->position, ['GK', 'DEF'])) {
            $points -= floor($this->goals_conceded / 2); // خصم نقطة لكل هدفين مستقبلين
        }

        // نقاط المشاركة
        if ($this->minutes_played >= 60) {
            $points += 2; // نقطتان للعب 60 دقيقة أو أكثر
        } elseif ($this->minutes_played > 0) {
            $points += 1; // نقطة واحدة للعب أقل من 60 دقيقة
        }

        return max(0, $points); // لا تقل النقاط عن صفر
    }
}
