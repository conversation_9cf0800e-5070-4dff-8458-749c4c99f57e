<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PointsHistory extends Model
{
    /**
     * الجدول المرتبط بالنموذج
     */
    protected $table = 'points_history';

    /**
     * الخصائص التي يمكن تعيينها بشكل جماعي
     */
    protected $fillable = [
        'player_id',
        'user_id',
        'points',
        'breakdown',
        'stats',
        'gameweek',
        'notes',
        'calculated_at'
    ];

    /**
     * الخصائص التي يجب تحويلها
     */
    protected $casts = [
        'breakdown' => 'array',
        'stats' => 'array',
        'calculated_at' => 'datetime'
    ];

    /**
     * علاقة مع اللاعب
     */
    public function player(): BelongsTo
    {
        return $this->belongsTo(Player::class);
    }

    /**
     * علاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
