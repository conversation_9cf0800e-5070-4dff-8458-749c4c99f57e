<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'group',
        'type',
        'description',
        'is_public'
    ];

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        // Try to get from cache first
        $cacheKey = 'setting_' . $key;
        
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        // If not in cache, get from database
        $setting = self::where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }
        
        $value = $setting->value;
        
        // Convert value based on type
        switch ($setting->type) {
            case 'boolean':
                $value = (bool) $value;
                break;
            case 'integer':
                $value = (int) $value;
                break;
            case 'float':
                $value = (float) $value;
                break;
            case 'array':
            case 'json':
                $value = json_decode($value, true);
                break;
        }
        
        // Store in cache for future requests
        Cache::put($cacheKey, $value, now()->addHours(24));
        
        return $value;
    }

    /**
     * Set a setting value
     *
     * @param string $key
     * @param mixed $value
     * @param string $group
     * @param string $type
     * @return Setting
     */
    public static function set(string $key, $value, string $group = 'general', string $type = 'string')
    {
        // Prepare value for storage
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value);
            $type = 'json';
        } elseif (is_bool($value)) {
            $value = $value ? '1' : '0';
            $type = 'boolean';
        }
        
        // Update or create the setting
        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'group' => $group,
                'type' => $type
            ]
        );
        
        // Update cache
        $cacheKey = 'setting_' . $key;
        Cache::put($cacheKey, $value, now()->addHours(24));
        
        return $setting;
    }

    /**
     * Get all settings as key-value pairs
     *
     * @param string|null $group Filter by group
     * @param bool $publicOnly Get only public settings
     * @return array
     */
    public static function getAllSettings(?string $group = null, bool $publicOnly = false)
    {
        $query = self::query();
        
        if ($group) {
            $query->where('group', $group);
        }
        
        if ($publicOnly) {
            $query->where('is_public', true);
        }
        
        $settings = $query->get();
        
        $result = [];
        
        foreach ($settings as $setting) {
            $value = $setting->value;
            
            // Convert value based on type
            switch ($setting->type) {
                case 'boolean':
                    $value = (bool) $value;
                    break;
                case 'integer':
                    $value = (int) $value;
                    break;
                case 'float':
                    $value = (float) $value;
                    break;
                case 'array':
                case 'json':
                    $value = json_decode($value, true);
                    break;
            }
            
            $result[$setting->key] = $value;
        }
        
        return $result;
    }
}