<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\NewsService;

class NewsController extends Controller
{
    protected $newsService;

    public function __construct(NewsService $newsService)
    {
        $this->newsService = $newsService;
    }

    /**
     * Get latest news via API
     */
    public function getLatestNews(Request $request)
    {
        $limit = $request->get('limit', 10);
        $news = $this->newsService->getPremierLeagueNews($limit);

        return response()->json([
            'success' => true,
            'data' => $news,
            'count' => count($news)
        ]);
    }

    /**
     * Get transfer news via API
     */
    public function getTransferNews(Request $request)
    {
        $limit = $request->get('limit', 6);
        $news = $this->newsService->getTransferNews($limit);

        return response()->json([
            'success' => true,
            'data' => $news,
            'count' => count($news)
        ]);
    }

    /**
     * Get trending topics
     */
    public function getTrendingTopics()
    {
        $topics = $this->newsService->getTrendingTopics();

        return response()->json([
            'success' => true,
            'data' => $topics
        ]);
    }
}
