<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\FPLApiService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FPLController extends Controller
{
    protected $fplService;

    public function __construct(FPLApiService $fplService)
    {
        $this->fplService = $fplService;
        $this->middleware('auth');
    }

    /**
     * Show FPL connection page
     */
    public function connect()
    {
        $user = Auth::user();
        
        return view('fpl.connect', [
            'user' => $user,
            'hasAccount' => $user->hasFPLAccount()
        ]);
    }

    /**
     * Connect FPL account
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'manager_id' => 'required|numeric|min:1'
        ], [
            'manager_id.required' => 'رقم المدير مطلوب',
            'manager_id.numeric' => 'رقم المدير يجب أن يكون رقماً',
            'manager_id.min' => 'رقم المدير غير صحيح'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $managerId = $request->manager_id;

        // Validate manager ID exists
        if (!$this->fplService->validateManagerId($managerId)) {
            return back()->withErrors([
                'manager_id' => 'رقم المدير غير موجود. تأكد من الرقم وحاول مرة أخرى.'
            ])->withInput();
        }

        try {
            // Get manager profile data
            $managerProfile = $this->fplService->getManagerProfile($managerId);
            
            if (!$managerProfile) {
                return back()->withErrors([
                    'manager_id' => 'حدث خطأ في جلب بيانات الحساب. حاول مرة أخرى.'
                ])->withInput();
            }

            // Format and save data
            $userData = $this->fplService->formatManagerDataForStorage($managerProfile);
            
            if (!$userData) {
                return back()->withErrors([
                    'manager_id' => 'حدث خطأ في معالجة البيانات. حاول مرة أخرى.'
                ])->withInput();
            }

            // Update user
            Auth::user()->update($userData);

            return redirect()->route('fpl.profile')->with('success', 'تم ربط حساب Fantasy Premier League بنجاح!');

        } catch (\Exception $e) {
            \Log::error('FPL Connection Error: ' . $e->getMessage());
            
            return back()->withErrors([
                'manager_id' => 'حدث خطأ غير متوقع. حاول مرة أخرى لاحقاً.'
            ])->withInput();
        }
    }

    /**
     * Show FPL profile
     */
    public function profile()
    {
        $user = Auth::user();
        
        if (!$user->hasFPLAccount()) {
            return redirect()->route('fpl.connect')->with('error', 'يجب ربط حساب Fantasy Premier League أولاً');
        }

        // Update data if needed
        if ($user->needsFPLUpdate()) {
            $this->updateUserFPLData($user);
        }

        $fplData = $user->getFPLData();
        $currentGameweek = $this->fplService->getCurrentGameweek();

        return view('fpl.profile', [
            'user' => $user,
            'fplData' => $fplData,
            'currentGameweek' => $currentGameweek
        ]);
    }

    /**
     * Update FPL data
     */
    public function update()
    {
        $user = Auth::user();
        
        if (!$user->hasFPLAccount()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد حساب FPL مربوط'
            ], 400);
        }

        try {
            $updated = $this->updateUserFPLData($user);
            
            if ($updated) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديث البيانات بنجاح',
                    'data' => [
                        'total_points' => $user->fpl_total_points,
                        'overall_rank' => $user->getFormattedOverallRank(),
                        'gameweek_points' => $user->fpl_gameweek_points,
                        'team_value' => $user->getFormattedTeamValue(),
                        'bank' => $user->getFormattedBank(),
                        'last_updated' => $user->fpl_last_updated->format('d/m/Y H:i')
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ في تحديث البيانات'
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('FPL Update Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ غير متوقع'
            ], 500);
        }
    }

    /**
     * Disconnect FPL account
     */
    public function disconnect()
    {
        $user = Auth::user();
        
        $user->update([
            'fpl_manager_id' => null,
            'fpl_team_name' => null,
            'fpl_manager_name' => null,
            'fpl_total_points' => 0,
            'fpl_overall_rank' => null,
            'fpl_gameweek_points' => 0,
            'fpl_team_value' => 100.0,
            'fpl_bank' => 0.0,
            'fpl_data' => null,
            'fpl_last_updated' => null,
            'fpl_connected' => false
        ]);

        return redirect()->route('fpl.connect')->with('success', 'تم إلغاء ربط حساب Fantasy Premier League');
    }

    /**
     * Helper method to update user FPL data
     */
    private function updateUserFPLData($user)
    {
        try {
            $managerProfile = $this->fplService->getManagerProfile($user->fpl_manager_id);
            
            if (!$managerProfile) {
                return false;
            }

            $userData = $this->fplService->formatManagerDataForStorage($managerProfile);
            
            if (!$userData) {
                return false;
            }

            $user->update($userData);
            return true;

        } catch (\Exception $e) {
            \Log::error('FPL Data Update Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get team details for current gameweek
     */
    public function teamDetails()
    {
        $user = Auth::user();
        
        if (!$user->hasFPLAccount()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد حساب FPL مربوط'
            ], 400);
        }

        try {
            $currentTeam = $this->fplService->getManagerTeam($user->fpl_manager_id);
            $allPlayers = $this->fplService->getAllPlayers();
            $allTeams = $this->fplService->getAllTeams();

            if (!$currentTeam || !$allPlayers || !$allTeams) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ في جلب بيانات الفريق'
                ], 500);
            }

            // Process team data
            $teamData = $this->processTeamData($currentTeam, $allPlayers, $allTeams);

            return response()->json([
                'success' => true,
                'data' => $teamData
            ]);

        } catch (\Exception $e) {
            \Log::error('FPL Team Details Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ غير متوقع'
            ], 500);
        }
    }

    /**
     * Process team data for display
     */
    private function processTeamData($teamData, $allPlayers, $allTeams)
    {
        $playersById = collect($allPlayers)->keyBy('id');
        $teamsById = collect($allTeams)->keyBy('id');

        $picks = collect($teamData['picks'])->map(function ($pick) use ($playersById, $teamsById) {
            $player = $playersById->get($pick['element']);
            $team = $teamsById->get($player['team']);

            return [
                'id' => $player['id'],
                'name' => $player['web_name'],
                'full_name' => $player['first_name'] . ' ' . $player['second_name'],
                'position' => $this->getPositionName($player['element_type']),
                'team' => $team['short_name'],
                'price' => $player['now_cost'] / 10,
                'points' => $player['total_points'],
                'is_captain' => $pick['is_captain'],
                'is_vice_captain' => $pick['is_vice_captain'],
                'multiplier' => $pick['multiplier']
            ];
        });

        return [
            'picks' => $picks,
            'active_chip' => $teamData['active_chip'],
            'automatic_subs' => $teamData['automatic_subs'] ?? [],
            'entry_history' => $teamData['entry_history']
        ];
    }

    /**
     * Get position name by type
     */
    private function getPositionName($elementType)
    {
        $positions = [
            1 => 'GKP',
            2 => 'DEF', 
            3 => 'MID',
            4 => 'FWD'
        ];

        return $positions[$elementType] ?? 'Unknown';
    }
}
