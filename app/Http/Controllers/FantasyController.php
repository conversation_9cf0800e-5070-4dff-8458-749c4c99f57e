<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Team;
use App\Models\Player;
use App\Models\FantasyTeam;
use App\Services\PointsCalculator;

class FantasyController extends Controller
{
    public function index()
    {
        $teams = Team::with('players')->get();
        return view('fantasy.index', compact('teams'));
    }

    public function createTeam()
    {
        $teams = Team::with('players')->get();
        return view('fantasy.create-team', compact('teams'));
    }

    public function storeTeam(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'manager_name' => 'required|string|max:255',
            'players' => 'required|array|size:15', // 11 أساسي + 4 احتياط
            'captain' => 'required|integer|exists:players,id',
            'vice_captain' => 'required|integer|exists:players,id|different:captain',
            'formation' => 'required|array'
        ]);

        $fantasyTeam = FantasyTeam::create([
            'name' => $request->name,
            'manager_name' => $request->manager_name,
            'formation' => $request->formation
        ]);

        // إضافة اللاعبين للفريق
        foreach ($request->players as $index => $playerId) {
            $isPlaying = $index < 11; // أول 11 لاعب في التشكيلة الأساسية
            $isCaptain = $playerId == $request->captain;
            $isViceCaptain = $playerId == $request->vice_captain;

            $fantasyTeam->players()->attach($playerId, [
                'is_captain' => $isCaptain,
                'is_vice_captain' => $isViceCaptain,
                'is_playing' => $isPlaying,
                'position_order' => $index + 1
            ]);
        }

        // حساب القيمة الإجمالية
        $totalValue = $fantasyTeam->calculateTotalValue();
        $fantasyTeam->update(['total_value' => $totalValue]);

        return redirect()->route('fantasy.team', $fantasyTeam->id)
            ->with('success', 'تم إنشاء الفريق بنجاح!');
    }

    public function showTeam(FantasyTeam $team)
    {
        $team->load(['players.team']);
        $pointsData = PointsCalculator::calculateTotalTeamPoints($team);

        return view('fantasy.team', compact('team', 'pointsData'));
    }

    public function updatePlayerStats(Request $request, Player $player)
    {
        $request->validate([
            'goals' => 'integer|min:0',
            'assists' => 'integer|min:0',
            'clean_sheets' => 'integer|min:0',
            'goals_conceded' => 'integer|min:0',
            'saves' => 'integer|min:0',
            'penalty_saves' => 'integer|min:0',
            'penalty_misses' => 'integer|min:0',
            'yellow_cards' => 'integer|min:0',
            'red_cards' => 'integer|min:0',
            'own_goals' => 'integer|min:0',
            'minutes_played' => 'integer|min:0|max:90'
        ]);

        $player->update($request->only([
            'goals', 'assists', 'clean_sheets', 'goals_conceded',
            'saves', 'penalty_saves', 'penalty_misses',
            'yellow_cards', 'red_cards', 'own_goals', 'minutes_played'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث إحصائيات اللاعب بنجاح',
            'points' => PointsCalculator::calculatePlayerPoints($player)
        ]);
    }

    public function getTeamPoints(FantasyTeam $team)
    {
        $pointsData = PointsCalculator::calculateTotalTeamPoints($team);
        return response()->json($pointsData);
    }

    // API Methods for Dynamic Operations
    public function getPlayers(Request $request)
    {
        $query = Player::with('team');

        if ($request->has('position')) {
            $query->where('position', $request->position);
        }

        if ($request->has('team_id')) {
            $query->where('team_id', $request->team_id);
        }

        if ($request->has('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $players = $query->get()->map(function ($player) {
            return [
                'id' => $player->id,
                'name' => $player->name,
                'position' => $player->position,
                'price' => $player->price,
                'team' => $player->team->name,
                'team_id' => $player->team_id,
                'points' => PointsCalculator::calculatePlayerPoints($player),
                'goals' => $player->goals,
                'assists' => $player->assists,
                'clean_sheets' => $player->clean_sheets,
                'minutes_played' => $player->minutes_played,
            ];
        });

        return response()->json($players);
    }

    public function getTeams()
    {
        $teams = Team::all()->map(function ($team) {
            return [
                'id' => $team->id,
                'name' => $team->name,
                'logo' => $team->logo,
            ];
        });

        return response()->json($teams);
    }

    public function createTeamAjax(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'manager_name' => 'required|string|max:255',
                'players' => 'required|array|size:15',
                'players.*' => 'exists:players,id',
                'captain' => 'required|exists:players,id',
                'vice_captain' => 'required|exists:players,id|different:captain',
                'formation' => 'required|array',
            ]);

            // Check budget constraint
            $players = Player::whereIn('id', $validated['players'])->get();
            $totalValue = $players->sum('price');

            if ($totalValue > 100) {
                return response()->json([
                    'success' => false,
                    'message' => 'تجاوزت الحد الأقصى للميزانية (100 مليون)'
                ], 400);
            }

            // Create team
            $team = FantasyTeam::create([
                'name' => $validated['name'],
                'manager_name' => $validated['manager_name'],
                'formation' => $validated['formation'],
                'total_value' => $totalValue,
                'user_id' => auth()->id(),
            ]);

            // Attach players
            foreach ($validated['players'] as $index => $playerId) {
                $team->players()->attach($playerId, [
                    'is_captain' => $playerId == $validated['captain'],
                    'is_vice_captain' => $playerId == $validated['vice_captain'],
                    'is_playing' => $index < 11, // First 11 are starters
                    'position_order' => $index + 1,
                ]);
            }

            $totalPoints = PointsCalculator::calculateTotalTeamPoints($team);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الفريق بنجاح!',
                'team' => [
                    'id' => $team->id,
                    'name' => $team->name,
                    'manager_name' => $team->manager_name,
                    'total_value' => $team->total_value,
                    'total_points' => $totalPoints['total_points'],
                    'formation' => $team->formation,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الفريق: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getUserTeams()
    {
        $teams = auth()->user()->fantasyTeams()->with('players.team')->get()->map(function ($team) {
            $pointsData = PointsCalculator::calculateTotalTeamPoints($team);
            return [
                'id' => $team->id,
                'name' => $team->name,
                'manager_name' => $team->manager_name,
                'total_value' => $team->total_value,
                'total_points' => $pointsData['total_points'],
                'formation' => $team->formation,
                'players_count' => $team->players->count(),
                'created_at' => $team->created_at->format('Y-m-d'),
            ];
        });

        return response()->json($teams);
    }

    public function getUserStats()
    {
        $user = auth()->user();
        $teams = $user->fantasyTeams()->with('players')->get();

        $totalPoints = 0;
        $totalValue = 0;

        foreach ($teams as $team) {
            $pointsData = PointsCalculator::calculateTotalTeamPoints($team);
            $totalPoints += $pointsData['total_points'];
            $totalValue += $team->total_value;
        }

        return response()->json([
            'teams_count' => $teams->count(),
            'total_points' => $totalPoints,
            'average_points' => $teams->count() > 0 ? round($totalPoints / $teams->count(), 1) : 0,
            'total_value' => $totalValue,
            'remaining_budget' => (100 * $teams->count()) - $totalValue,
        ]);
    }

    public function updatePlayerStatsAjax(Request $request, Player $player)
    {
        try {
            $validated = $request->validate([
                'goals' => 'integer|min:0',
                'assists' => 'integer|min:0',
                'clean_sheets' => 'integer|min:0',
                'goals_conceded' => 'integer|min:0',
                'saves' => 'integer|min:0',
                'penalty_saves' => 'integer|min:0',
                'penalty_misses' => 'integer|min:0',
                'yellow_cards' => 'integer|min:0',
                'red_cards' => 'integer|min:0',
                'own_goals' => 'integer|min:0',
                'minutes_played' => 'integer|min:0|max:90'
            ]);

            $player->update($validated);
            $newPoints = PointsCalculator::calculatePlayerPoints($player);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث إحصائيات اللاعب بنجاح',
                'player' => [
                    'id' => $player->id,
                    'name' => $player->name,
                    'points' => $newPoints,
                    'stats' => $validated,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإحصائيات: ' . $e->getMessage()
            ], 500);
        }
    }

    public function calculatePointsAjax(FantasyTeam $team)
    {
        try {
            $pointsData = PointsCalculator::calculateTotalTeamPoints($team);

            return response()->json([
                'success' => true,
                'team_id' => $team->id,
                'points_data' => $pointsData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حساب النقاط: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteTeamAjax(FantasyTeam $team)
    {
        try {
            // Check if user owns the team
            if ($team->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بحذف هذا الفريق'
                ], 403);
            }

            $team->players()->detach();
            $team->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الفريق بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الفريق: ' . $e->getMessage()
            ], 500);
        }
    }
}
