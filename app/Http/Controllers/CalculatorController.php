<?php

namespace App\Http\Controllers;

use App\Models\Player;
use App\Models\PointsHistory;
use App\Services\PointsCalculator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class CalculatorController extends Controller
{
    /**
     * عرض صفحة الرئيسية للحاسبة
     */
    public function index()
    {
        return view('calculator.index');
    }

    /**
     * عرض صفحة الحاسبة
     */
    public function calculator()
    {
        return view('calculator.calculator', [
            'playerData' => Session::get('player_data', [])
        ]);
    }


    /**
     * Display the points calculator page
     *
     * @return \Illuminate\View\View
     */
    public function pointsCalculator()
    {
        return view('calculator.points-calculator');
    }
    
    /**
     * Calculate points based on round data
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateRoundPoints(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'round_score' => 'required|integer|min:0',
            'round_number' => 'required|integer|min:1|max:38',
            'match_status' => 'nullable|in:win,draw,loss',
            'opponent_more_players' => 'nullable|in:yes,no',
            'has_expensive_player' => 'nullable|in:yes,no',
            'expensive_player_name' => 'nullable|string|max:255',
            'expensive_player_points' => 'nullable|integer|min:0',
            'team_won_league' => 'nullable|in:yes,no',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Get validated data
        $data = $validator->validated();
        
        // Initialize total points with round score
        $totalPoints = $data['round_score'];
        $breakdown = [
            [
                'label' => __('app.round_score'),
                'points' => $data['round_score'],
                'type' => 'neutral'
            ]
        ];
        
        // Add/subtract points based on match status
        if (isset($data['match_status'])) {
            $matchPoints = 0;
            $matchLabel = '';
            $matchType = '';
            
            if ($data['match_status'] === 'win') {
                $matchPoints = 10;
                $matchLabel = __('app.win_bonus');
                $matchType = 'positive';
            } else if ($data['match_status'] === 'draw') {
                $matchPoints = -5;
                $matchLabel = __('app.draw_penalty');
                $matchType = 'negative';
            } else if ($data['match_status'] === 'loss') {
                $matchPoints = -10;
                $matchLabel = __('app.loss_penalty');
                $matchType = 'negative';
            }
            
            $totalPoints += $matchPoints;
            
            $breakdown[] = [
                'label' => $matchLabel,
                'points' => $matchPoints,
                'type' => $matchType
            ];
        }
        
        // Check opponent players (round 1 only)
        if ($data['round_number'] === 1 && isset($data['opponent_more_players']) && $data['opponent_more_players'] === 'yes') {
            $totalPoints -= 5;
            
            $breakdown[] = [
                'label' => __('app.opponent_players_penalty'),
                'points' => -5,
                'type' => 'negative'
            ];
        }
        
        // Check expensive player
        if (isset($data['has_expensive_player']) && $data['has_expensive_player'] === 'yes' && 
            isset($data['expensive_player_name']) && isset($data['expensive_player_points'])) {
            $playerPoints = (int) $data['expensive_player_points'];
            
            if ($playerPoints > 0) {
                $totalPoints -= $playerPoints;
                
                $breakdown[] = [
                    'label' => __('app.expensive_player_penalty') . ': ' . $data['expensive_player_name'],
                    'points' => -$playerPoints,
                    'type' => 'negative'
                ];
            }
        }
        
        // Check league winner (round 38 only)
        if ($data['round_number'] === 38 && isset($data['team_won_league'])) {
            $leaguePoints = 0;
            $leagueLabel = '';
            $leagueType = '';
            
            if ($data['team_won_league'] === 'yes') {
                $leaguePoints = 50;
                $leagueLabel = __('app.league_winner_bonus');
                $leagueType = 'positive';
            } else if ($data['team_won_league'] === 'no') {
                $leaguePoints = -50;
                $leagueLabel = __('app.league_loser_penalty');
                $leagueType = 'negative';
            }
            
            $totalPoints += $leaguePoints;
            
            $breakdown[] = [
                'label' => $leagueLabel,
                'points' => $leaguePoints,
                'type' => $leagueType
            ];
        }
        
        // Return the calculated points and breakdown
        return response()->json([
            'totalPoints' => $totalPoints,
            'breakdown' => $breakdown
        ]);
    }

    /**
     * حساب النقاط بناءً على إحصائيات اللاعب
     */
    public function calculate(Request $request)
    {
        // التحقق من صحة البيانات المدخلة
        $validator = Validator::make($request->all(), [
            'player_name' => 'required|string|max:255',
            'position' => 'required|in:GK,DEF,MID,FWD',
            'goals' => 'required|integer|min:0|max:50',
            'assists' => 'required|integer|min:0|max:50',
            'minutes_played' => 'required|integer|min:0|max:90',
            'clean_sheets' => 'required|integer|min:0|max:10',
            'goals_conceded' => 'required|integer|min:0|max:50',
            'saves' => 'required|integer|min:0|max:50',
            'penalty_saves' => 'required|integer|min:0|max:10',
            'penalty_misses' => 'required|integer|min:0|max:10',
            'yellow_cards' => 'required|integer|min:0|max:10',
            'red_cards' => 'required|integer|min:0|max:5',
            'own_goals' => 'required|integer|min:0|max:10',
            'save_record' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => __('app.invalid_input'),
                'errors' => $validator->errors()
            ], 422);
        }

        // حفظ بيانات اللاعب في الجلسة
        Session::put('player_data', $request->all());

        // حساب النقاط
        $points = $this->calculatePoints($request);
        
        // حفظ سجل النقاط إذا طلب المستخدم ذلك
        if ($request->input('save_record', false)) {
            $pointsHistory = $this->savePointsHistory($points, $request);
            $points['record_saved'] = true;
            $points['record_id'] = $pointsHistory->id;
        }

        return response()->json([
            'success' => true,
            'message' => __('app.points_calculated'),
            'data' => $points
        ]);
    }

    /**
     * تغيير لغة الواجهة
     */
    public function setLanguage(Request $request)
    {
        $locale = $request->input('locale', 'ar');
        App::setLocale($locale);
        Session::put('locale', $locale);
        
        return redirect()->back();
    }
    
    /**
     * حفظ سجل النقاط المحسوبة
     */
    private function savePointsHistory(array $pointsData, Request $request)
    {
        // البحث عن اللاعب في قاعدة البيانات أو إنشاء سجل جديد للنقاط
        $player = Player::where('name', $request->input('player_name'))->first();
        
        $stats = [
            'goals' => (int) $request->input('goals', 0),
            'assists' => (int) $request->input('assists', 0),
            'minutes_played' => (int) $request->input('minutes_played', 0),
            'clean_sheets' => (int) $request->input('clean_sheets', 0),
            'goals_conceded' => (int) $request->input('goals_conceded', 0),
            'saves' => (int) $request->input('saves', 0),
            'penalty_saves' => (int) $request->input('penalty_saves', 0),
            'penalty_misses' => (int) $request->input('penalty_misses', 0),
            'yellow_cards' => (int) $request->input('yellow_cards', 0),
            'red_cards' => (int) $request->input('red_cards', 0),
            'own_goals' => (int) $request->input('own_goals', 0),
            'position' => $request->input('position')
        ];
        
        // إنشاء سجل جديد لتاريخ النقاط
        $pointsHistory = new PointsHistory([
            'player_id' => $player ? $player->id : null,
            'user_id' => Auth::check() ? Auth::id() : null,
            'points' => $pointsData['total_points'],
            'breakdown' => $pointsData['breakdown'],
            'stats' => $stats,
            'gameweek' => Session::get('current_gameweek', null),
            'calculated_at' => now()
        ]);
        
        // إذا لم يتم العثور على اللاعب، نحفظ اسم اللاعب في حقل الملاحظات
        if (!$player) {
            $pointsHistory->notes = 'Player name: ' . $request->input('player_name');
        }
        
        $pointsHistory->save();
        
        return $pointsHistory;
    }

    /**
     * حساب النقاط بناءً على المدخلات
     */
    private function calculatePoints(Request $request)
    {
        $position = $request->input('position');
        $goals = (int) $request->input('goals', 0);
        $assists = (int) $request->input('assists', 0);
        $minutesPlayed = (int) $request->input('minutes_played', 0);
        $cleanSheets = (int) $request->input('clean_sheets', 0);
        $goalsConceded = (int) $request->input('goals_conceded', 0);
        $saves = (int) $request->input('saves', 0);
        $penaltySaves = (int) $request->input('penalty_saves', 0);
        $penaltyMisses = (int) $request->input('penalty_misses', 0);
        $yellowCards = (int) $request->input('yellow_cards', 0);
        $redCards = (int) $request->input('red_cards', 0);
        $ownGoals = (int) $request->input('own_goals', 0);

        $points = 0;
        $breakdown = [];

        // 1. نقاط المشاركة
        if ($minutesPlayed >= 60) {
            $points += 2;
            $breakdown[] = [
                'label' => __('app.appearance_points'),
                'value' => '+2',
                'type' => 'positive'
            ];
        } elseif ($minutesPlayed > 0) {
            $points += 1;
            $breakdown[] = [
                'label' => __('app.appearance_points'),
                'value' => '+1',
                'type' => 'positive'
            ];
        }

        // 2. نقاط الأهداف حسب المركز
        $goalPoints = 0;
        switch ($position) {
            case 'GK':
            case 'DEF':
                $goalPoints = $goals * 6;
                break;
            case 'MID':
                $goalPoints = $goals * 5;
                break;
            case 'FWD':
                $goalPoints = $goals * 4;
                break;
        }

        if ($goalPoints > 0) {
            $points += $goalPoints;
            $breakdown[] = [
                'label' => __('app.goal_points'),
                'value' => '+' . $goalPoints,
                'type' => 'positive'
            ];
        }

        // 3. نقاط التمريرات الحاسمة
        $assistPoints = $assists * 3;
        if ($assistPoints > 0) {
            $points += $assistPoints;
            $breakdown[] = [
                'label' => __('app.assist_points'),
                'value' => '+' . $assistPoints,
                'type' => 'positive'
            ];
        }

        // 4. نقاط الشباك النظيفة (للحراس والمدافعين فقط)
        if (in_array($position, ['GK', 'DEF']) && $cleanSheets > 0) {
            $cleanSheetPoints = $cleanSheets * 4;
            $points += $cleanSheetPoints;
            $breakdown[] = [
                'label' => __('app.clean_sheet_points'),
                'value' => '+' . $cleanSheetPoints,
                'type' => 'positive'
            ];
        }

        // 5. نقاط الإنقاذات (للحراس فقط)
        if ($position === 'GK' && $saves > 0) {
            $savePoints = floor($saves / 3);
            if ($savePoints > 0) {
                $points += $savePoints;
                $breakdown[] = [
                    'label' => __('app.save_points'),
                    'value' => '+' . $savePoints,
                    'type' => 'positive'
                ];
            }
        }

        // 6. نقاط إنقاذ ركلات الجزاء (للحراس فقط)
        if ($position === 'GK' && $penaltySaves > 0) {
            $penaltySavePoints = $penaltySaves * 5;
            $points += $penaltySavePoints;
            $breakdown[] = [
                'label' => __('app.penalty_save_points'),
                'value' => '+' . $penaltySavePoints,
                'type' => 'positive'
            ];
        }

        // 7. خصم نقاط البطاقات الصفراء
        if ($yellowCards > 0) {
            $yellowCardPoints = $yellowCards * -1;
            $points += $yellowCardPoints;
            $breakdown[] = [
                'label' => __('app.yellow_card_points'),
                'value' => $yellowCardPoints,
                'type' => 'negative'
            ];
        }

        // 8. خصم نقاط البطاقات الحمراء
        if ($redCards > 0) {
            $redCardPoints = $redCards * -3;
            $points += $redCardPoints;
            $breakdown[] = [
                'label' => __('app.red_card_points'),
                'value' => $redCardPoints,
                'type' => 'negative'
            ];
        }

        // 9. خصم نقاط الأهداف في المرمى
        if ($ownGoals > 0) {
            $ownGoalPoints = $ownGoals * -2;
            $points += $ownGoalPoints;
            $breakdown[] = [
                'label' => __('app.own_goal_points'),
                'value' => $ownGoalPoints,
                'type' => 'negative'
            ];
        }

        // 10. خصم نقاط ركلات الجزاء الضائعة
        if ($penaltyMisses > 0) {
            $penaltyMissPoints = $penaltyMisses * -2;
            $points += $penaltyMissPoints;
            $breakdown[] = [
                'label' => __('app.penalty_miss_points'),
                'value' => $penaltyMissPoints,
                'type' => 'negative'
            ];
        }

        // 11. خصم نقاط الأهداف المستقبلة (للحراس والمدافعين)
        if (in_array($position, ['GK', 'DEF']) && $goalsConceded > 0) {
            $goalsConcededPoints = floor($goalsConceded / 2) * -1;
            if ($goalsConcededPoints < 0) {
                $points += $goalsConcededPoints;
                $breakdown[] = [
                    'label' => __('app.goals_conceded_points'),
                    'value' => $goalsConcededPoints,
                    'type' => 'negative'
                ];
            }
        }

        // التأكد من أن النقاط لا تقل عن صفر
        $points = max(0, $points);

        return [
            'player_name' => $request->input('player_name'),
            'position' => $position,
            'total_points' => $points,
            'breakdown' => $breakdown
        ];
    }
}