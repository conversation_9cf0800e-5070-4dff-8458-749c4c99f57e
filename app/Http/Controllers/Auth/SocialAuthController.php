<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Socialite\Facades\Socialite;

class SocialAuthController extends Controller
{
    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\Response
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Obtain the user information from Google.
     *
     * @return \Illuminate\Http\Response
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            
            // Check if user already exists
            $user = User::where('email', $googleUser->getEmail())->first();
            
            // If user doesn't exist, create a new one
            if (!$user) {
                $user = User::create([
                    'name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    'password' => Hash::make(Str::random(16)),
                    // FPL fields are left null, user can connect later
                    'fpl_manager_id' => null,
                    'fpl_team_name' => null,
                    'fpl_team_id' => null,
                    'fpl_summary_overall_points' => null,
                    'fpl_summary_overall_rank' => null,
                    'fpl_summary_gameweek_points' => null,
                    'fpl_summary_gameweek_rank' => null,
                ]);
            }
            
            // Login the user
            Auth::login($user);
            
            // Redirect to FPL profile if user has FPL account, otherwise to connect page
            if ($user->hasFPLAccount()) {
                return redirect()->route('fpl.profile');
            } else {
                return redirect()->route('fpl.connect');
            }
            
        } catch (\Exception $e) {
            return redirect()->route('login')
                ->with('error', 'حدث خطأ أثناء تسجيل الدخول باستخدام Google. يرجى المحاولة مرة أخرى.');
        }
    }
}