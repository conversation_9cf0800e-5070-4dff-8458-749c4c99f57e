<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\FPLApiService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    protected $fplService;

    public function __construct(FPLApiService $fplService)
    {
        $this->fplService = $fplService;
    }

    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        try {
            // Create user without FPL data
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'fpl_manager_id' => null,
                'fpl_connected' => false
            ];

            $user = User::create($userData);

            event(new Registered($user));
            Auth::login($user);

            return redirect()->route('profile.edit')->with('success', __('auth.registration_success'));

        } catch (\Exception $e) {
            Log::error('Registration Error: ' . $e->getMessage());

            return back()->withErrors([
                'email' => __('auth.unexpected_error')
            ])->withInput();
        }
    }
}
