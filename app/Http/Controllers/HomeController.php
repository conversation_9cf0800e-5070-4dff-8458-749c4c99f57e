<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\NewsService;

class HomeController extends Controller
{
    protected $newsService;

    public function __construct(NewsService $newsService)
    {
        $this->newsService = $newsService;
    }

    /**
     * Display the homepage
     */
    public function index()
    {
        $data = [
            'latest_news' => $this->newsService->getPremierLeagueNews(6),
            'transfer_news' => $this->newsService->getTransferNews(4),
            'trending_topics' => $this->newsService->getTrendingTopics(),
            'hero_slides' => $this->getHeroSlides(),
            'features' => $this->getFeatures(),
            'stats' => $this->getStats()
        ];

        return view('home.index', $data);
    }

    /**
     * Get hero slider content
     */
    private function getHeroSlides()
    {
        return [
            [
                'title' => 'Fantasy Premier League 2024/25',
                'subtitle' => 'Build your dream team and compete with millions',
                'description' => 'Create your ultimate Premier League fantasy team with the best players from all 20 clubs.',
                'image' => 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
                'cta_text' => 'Start Playing',
                'cta_link' => '#register'
            ],
            [
                'title' => 'Live Match Updates',
                'subtitle' => 'Real-time scores and statistics',
                'description' => 'Get instant updates on all Premier League matches with detailed player statistics.',
                'image' => 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
                'cta_text' => 'Watch Demo',
                'cta_link' => '#demo'
            ],
            [
                'title' => 'AI-Powered Insights',
                'subtitle' => 'Smart predictions and analysis',
                'description' => 'Advanced AI algorithms provide you with the best team recommendations and transfer tips.',
                'image' => 'https://images.unsplash.com/photo-1526232761682-d26e03ac148e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
                'cta_text' => 'Learn More',
                'cta_link' => '#features'
            ]
        ];
    }

    /**
     * Get website features
     */
    private function getFeatures()
    {
        return [
            [
                'icon' => 'fas fa-users',
                'title' => 'Team Management',
                'description' => 'Build and manage your fantasy team with an intuitive interface'
            ],
            [
                'icon' => 'fas fa-chart-line',
                'title' => 'Live Statistics',
                'description' => 'Real-time player stats and match updates'
            ],
            [
                'icon' => 'fas fa-trophy',
                'title' => 'Competitions',
                'description' => 'Join leagues and compete with friends and family'
            ],
            [
                'icon' => 'fas fa-mobile-alt',
                'title' => 'Mobile Ready',
                'description' => 'Play anywhere with our responsive design'
            ],
            [
                'icon' => 'fas fa-brain',
                'title' => 'AI Insights',
                'description' => 'Smart recommendations powered by artificial intelligence'
            ],
            [
                'icon' => 'fas fa-globe',
                'title' => 'Multi-Language',
                'description' => 'Available in English, Arabic, and French'
            ]
        ];
    }

    /**
     * Get website statistics
     */
    private function getStats()
    {
        return [
            [
                'number' => '8M+',
                'label' => 'Active Players',
                'icon' => 'fas fa-users'
            ],
            [
                'number' => '20',
                'label' => 'Premier League Teams',
                'icon' => 'fas fa-shield-alt'
            ],
            [
                'number' => '600+',
                'label' => 'Players Available',
                'icon' => 'fas fa-running'
            ],
            [
                'number' => '38',
                'label' => 'Gameweeks',
                'icon' => 'fas fa-calendar'
            ]
        ];
    }

    /**
     * Handle registration
     */
    public function register(Request $request)
    {
        // For now, just redirect to official FPL site
        return redirect('https://fantasy.premierleague.com/');
    }

    /**
     * Set language
     */
    public function setLanguage(Request $request)
    {
        $language = $request->input('language');

        if (in_array($language, ['en', 'ar', 'fr'])) {
            session(['locale' => $language]);
        }

        return redirect()->back();
    }
}
