<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get supported languages from config
        $supportedLanguages = array_keys(config('languages.supported', ['en' => []]));
        $defaultLanguage = config('languages.default', 'en');

        // Get locale from session, default to config default
        $locale = Session::get('locale', $defaultLanguage);

        // Validate locale
        if (!in_array($locale, $supportedLanguages)) {
            $locale = $defaultLanguage;
        }

        // Set application locale
        App::setLocale($locale);

        return $next($request);
    }
}
