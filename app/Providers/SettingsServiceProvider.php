<?php

namespace App\Providers;

use App\Models\Setting;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only load settings if the settings table exists
        if (Schema::hasTable('settings')) {
            // Share settings with all views
            $settings = Setting::getAllSettings(null, true);
            View::share('settings', $settings);
            
            // Make theme colors available as CSS variables
            $themeSettings = Setting::getAllSettings('theme', true);
            View::share('themeSettings', $themeSettings);
        }
    }
}