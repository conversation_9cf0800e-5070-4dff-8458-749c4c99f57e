<?php

namespace App\Helpers;

use Illuminate\Support\Facades\App;

class LanguageHelper
{
    /**
     * Get all supported languages
     *
     * @return array
     */
    public static function getSupportedLanguages()
    {
        return config('languages.supported', [
            'en' => ['name' => 'English', 'flag' => '🇬🇧', 'dir' => 'ltr'],
            'ar' => ['name' => 'العربية', 'flag' => '🇸🇦', 'dir' => 'rtl'],
            'fr' => ['name' => 'Français', 'flag' => '🇫🇷', 'dir' => 'ltr'],
        ]);
    }
    
    /**
     * Get current language information
     *
     * @return array
     */
    public static function getCurrentLanguage()
    {
        $currentLocale = App::getLocale();
        $languages = self::getSupportedLanguages();
        
        return $languages[$currentLocale] ?? $languages['en'];
    }
    
    /**
     * Check if current language is RTL
     *
     * @return bool
     */
    public static function isRTL()
    {
        $currentLang = self::getCurrentLanguage();
        return ($currentLang['dir'] ?? 'ltr') === 'rtl';
    }
    
    /**
     * Get language direction
     *
     * @param string|null $locale
     * @return string
     */
    public static function getDirection($locale = null)
    {
        if (!$locale) {
            $locale = App::getLocale();
        }
        
        $languages = self::getSupportedLanguages();
        return $languages[$locale]['dir'] ?? 'ltr';
    }
    
    /**
     * Get language name
     *
     * @param string|null $locale
     * @return string
     */
    public static function getLanguageName($locale = null)
    {
        if (!$locale) {
            $locale = App::getLocale();
        }
        
        $languages = self::getSupportedLanguages();
        return $languages[$locale]['name'] ?? 'English';
    }
    
    /**
     * Get language flag
     *
     * @param string|null $locale
     * @return string
     */
    public static function getLanguageFlag($locale = null)
    {
        if (!$locale) {
            $locale = App::getLocale();
        }
        
        $languages = self::getSupportedLanguages();
        return $languages[$locale]['flag'] ?? '🇬🇧';
    }
    
    /**
     * Check if a locale is supported
     *
     * @param string $locale
     * @return bool
     */
    public static function isSupported($locale)
    {
        $languages = self::getSupportedLanguages();
        return array_key_exists($locale, $languages);
    }
    
    /**
     * Get default language
     *
     * @return string
     */
    public static function getDefaultLanguage()
    {
        return config('languages.default', 'en');
    }
    
    /**
     * Get fallback language
     *
     * @return string
     */
    public static function getFallbackLanguage()
    {
        return config('languages.fallback', 'en');
    }
}
