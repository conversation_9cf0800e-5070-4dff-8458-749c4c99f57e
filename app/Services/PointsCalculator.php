<?php

namespace App\Services;

use App\Models\Player;
use App\Models\FantasyTeam;

class PointsCalculator
{
    /**
     * حساب النقاط حسب القوانين المحددة في الصورة
     */
    public static function calculatePlayerPoints(Player $player): int
    {
        $points = 0;
        
        // 1. الفرعة - نقاط الأهداف حسب المركز
        switch ($player->position) {
            case 'GK':
            case 'DEF':
                $points += $player->goals * 6; // 6 نقاط لكل هدف للحراس والمدافعين
                break;
            case 'MID':
                $points += $player->goals * 5; // 5 نقاط لكل هدف لخط الوسط
                break;
            case 'FWD':
                $points += $player->goals * 4; // 4 نقاط لكل هدف للمهاجمين
                break;
        }
        
        // نقاط التمريرات الحاسمة
        $points += $player->assists * 3;
        
        // نقاط الشباك النظيفة (للحراس والمدافعين فقط)
        if (in_array($player->position, ['GK', 'DEF'])) {
            $points += $player->clean_sheets * 4;
        }
        
        // نقاط الإنقاذات (للحراس فقط)
        if ($player->position === 'GK') {
            $points += floor($player->saves / 3); // نقطة واحدة لكل 3 إنقاذات
            $points += $player->penalty_saves * 5; // 5 نقاط لكل ركلة جزاء محبطة
        }
        
        // خصم نقاط
        $points -= $player->yellow_cards * 1; // خصم نقطة لكل بطاقة صفراء
        $points -= $player->red_cards * 3; // خصم 3 نقاط لكل بطاقة حمراء
        $points -= $player->own_goals * 2; // خصم نقطتين لكل هدف في المرمى
        $points -= $player->penalty_misses * 2; // خصم نقطتين لكل ركلة جزاء ضائعة
        
        // خصم نقاط الأهداف المستقبلة (للحراس والمدافعين)
        if (in_array($player->position, ['GK', 'DEF'])) {
            $points -= floor($player->goals_conceded / 2); // خصم نقطة لكل هدفين مستقبلين
        }
        
        // نقاط المشاركة
        if ($player->minutes_played >= 60) {
            $points += 2; // نقطتان للعب 60 دقيقة أو أكثر
        } elseif ($player->minutes_played > 0) {
            $points += 1; // نقطة واحدة للعب أقل من 60 دقيقة
        }
        
        return max(0, $points); // لا تقل النقاط عن صفر
    }

    /**
     * 2. اختيار الفريق - التحقق من صحة التشكيلة
     */
    public static function validateTeamSelection(FantasyTeam $team): array
    {
        $errors = [];
        $playingPlayers = $team->playingPlayers;
        
        // التحقق من عدد اللاعبين (11 لاعب)
        if ($playingPlayers->count() !== 11) {
            $errors[] = 'يجب أن يكون عدد اللاعبين في التشكيلة 11 لاعباً';
        }
        
        // التحقق من التشكيلة
        $positions = $playingPlayers->groupBy('position');
        $gkCount = $positions->get('GK', collect())->count();
        $defCount = $positions->get('DEF', collect())->count();
        $midCount = $positions->get('MID', collect())->count();
        $fwdCount = $positions->get('FWD', collect())->count();
        
        if ($gkCount !== 1) {
            $errors[] = 'يجب اختيار حارس مرمى واحد فقط';
        }
        
        if ($defCount < 3 || $defCount > 5) {
            $errors[] = 'يجب اختيار من 3 إلى 5 مدافعين';
        }
        
        if ($midCount < 3 || $midCount > 5) {
            $errors[] = 'يجب اختيار من 3 إلى 5 لاعبي خط وسط';
        }
        
        if ($fwdCount < 1 || $fwdCount > 3) {
            $errors[] = 'يجب اختيار من 1 إلى 3 مهاجمين';
        }
        
        // التحقق من عدم وجود أكثر من 3 لاعبين من نفس الفريق
        $teamCounts = $playingPlayers->groupBy('team_id');
        foreach ($teamCounts as $teamId => $teamPlayers) {
            if ($teamPlayers->count() > 3) {
                $teamName = $teamPlayers->first()->team->name ?? "الفريق $teamId";
                $errors[] = "لا يمكن اختيار أكثر من 3 لاعبين من $teamName";
            }
        }
        
        // التحقق من وجود كابتن ونائب كابتن
        $captain = $team->captain();
        $viceCaptain = $team->viceCaptain();
        
        if (!$captain) {
            $errors[] = 'يجب اختيار كابتن للفريق';
        }
        
        if (!$viceCaptain) {
            $errors[] = 'يجب اختيار نائب كابتن للفريق';
        }
        
        if ($captain && $viceCaptain && $captain->id === $viceCaptain->id) {
            $errors[] = 'لا يمكن أن يكون الكابتن ونائب الكابتن نفس اللاعب';
        }
        
        return $errors;
    }

    /**
     * 3. قيمة اللاعبين في التشكيلة - التحقق من عدم تجاوز 100 مليون
     */
    public static function validateTeamValue(FantasyTeam $team): array
    {
        $errors = [];
        $totalValue = $team->calculateTotalValue();
        
        if ($totalValue > 100.0) {
            $errors[] = "قيمة الفريق ($totalValue مليون) تتجاوز الحد المسموح (100 مليون)";
        }
        
        return $errors;
    }

    /**
     * 4. التوقعات الإضافية (Bonus) - حساب النقاط الإضافية
     */
    public static function calculateBonusPoints(FantasyTeam $team): int
    {
        $bonusPoints = 0;
        $totalPoints = $team->calculateTotalPoints();
        
        // +50 نقطة: توقع الفريق الذي سيفوز بـ الدوري الإنجليزي (باستثناء ليفربول)
        // هذا يحتاج إلى تنفيذ منطق خاص حسب نتائج الدوري
        
        // +50 نقطة: توقع الفريق الذي سيهبط إلى الدرجة الثانية
        // هذا أيضاً يحتاج إلى تنفيذ منطق خاص
        
        return $bonusPoints;
    }

    /**
     * حساب النقاط الإجمالية للفريق مع تطبيق جميع القوانين
     */
    public static function calculateTotalTeamPoints(FantasyTeam $team): array
    {
        $result = [
            'total_points' => 0,
            'player_points' => [],
            'bonus_points' => 0,
            'errors' => []
        ];
        
        // التحقق من صحة التشكيلة
        $validationErrors = array_merge(
            self::validateTeamSelection($team),
            self::validateTeamValue($team)
        );
        
        if (!empty($validationErrors)) {
            $result['errors'] = $validationErrors;
            return $result;
        }
        
        // حساب نقاط اللاعبين
        $totalPoints = 0;
        foreach ($team->playingPlayers as $player) {
            $playerPoints = self::calculatePlayerPoints($player);
            
            // مضاعفة نقاط الكابتن
            if ($player->pivot->is_captain) {
                $playerPoints *= 2;
            }
            
            $result['player_points'][$player->id] = [
                'name' => $player->name,
                'position' => $player->position,
                'points' => $playerPoints,
                'is_captain' => $player->pivot->is_captain,
                'is_vice_captain' => $player->pivot->is_vice_captain
            ];
            
            $totalPoints += $playerPoints;
        }
        
        // حساب النقاط الإضافية
        $bonusPoints = self::calculateBonusPoints($team);
        
        $result['total_points'] = $totalPoints + $bonusPoints;
        $result['bonus_points'] = $bonusPoints;
        
        return $result;
    }
}
