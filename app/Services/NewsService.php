<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class NewsService
{
    private $apiKey;
    private $baseUrl = 'https://newsapi.org/v2';
    
    public function __construct()
    {
        $this->apiKey = env('NEWS_API_KEY', 'demo_key');
    }
    
    /**
     * Get Premier League news
     */
    public function getPremierLeagueNews($limit = 10)
    {
        $cacheKey = 'premier_league_news_' . $limit;
        
        return Cache::remember($cacheKey, 1800, function () use ($limit) { // 30 minutes cache
            try {
                $response = Http::timeout(10)->get($this->baseUrl . '/everything', [
                    'apiKey' => $this->apiKey,
                    'q' => 'Premier League OR "English Premier League" OR EPL',
                    'language' => 'en',
                    'sortBy' => 'publishedAt',
                    'pageSize' => $limit,
                    'domains' => 'bbc.com,skysports.com,espn.com,premierleague.com,goal.com'
                ]);
                
                if ($response->successful()) {
                    $articles = $response->json()['articles'] ?? [];
                    return $this->processArticles($articles);
                }
                
                return $this->getFallbackNews();
                
            } catch (\Exception $e) {
                Log::error('News API Error: ' . $e->getMessage());
                return $this->getFallbackNews();
            }
        });
    }
    
    /**
     * Get transfer news
     */
    public function getTransferNews($limit = 6)
    {
        $cacheKey = 'transfer_news_' . $limit;
        
        return Cache::remember($cacheKey, 3600, function () use ($limit) { // 1 hour cache
            try {
                $response = Http::timeout(10)->get($this->baseUrl . '/everything', [
                    'apiKey' => $this->apiKey,
                    'q' => 'Premier League transfer OR "football transfer" OR "soccer transfer"',
                    'language' => 'en',
                    'sortBy' => 'publishedAt',
                    'pageSize' => $limit,
                    'domains' => 'bbc.com,skysports.com,espn.com,transfermarkt.com'
                ]);
                
                if ($response->successful()) {
                    $articles = $response->json()['articles'] ?? [];
                    return $this->processArticles($articles);
                }
                
                return $this->getFallbackTransferNews();
                
            } catch (\Exception $e) {
                Log::error('Transfer News API Error: ' . $e->getMessage());
                return $this->getFallbackTransferNews();
            }
        });
    }
    
    /**
     * Process articles with AI-like enhancements
     */
    private function processArticles($articles)
    {
        $processed = [];
        
        foreach ($articles as $article) {
            if (empty($article['title']) || empty($article['description'])) {
                continue;
            }
            
            $processed[] = [
                'title' => $this->cleanTitle($article['title']),
                'description' => $this->enhanceDescription($article['description']),
                'url' => $article['url'],
                'image' => $article['urlToImage'] ?? $this->getDefaultImage(),
                'source' => $article['source']['name'] ?? 'Unknown',
                'published_at' => $this->formatDate($article['publishedAt']),
                'category' => $this->categorizeArticle($article['title'] . ' ' . $article['description']),
                'reading_time' => $this->estimateReadingTime($article['description']),
                'sentiment' => $this->analyzeSentiment($article['title'] . ' ' . $article['description'])
            ];
        }
        
        return $processed;
    }
    
    /**
     * Clean and enhance article titles
     */
    private function cleanTitle($title)
    {
        // Remove source names from titles
        $title = preg_replace('/\s*-\s*[A-Za-z\s]+$/', '', $title);
        return trim($title);
    }
    
    /**
     * Enhance descriptions with AI-like processing
     */
    private function enhanceDescription($description)
    {
        // Limit description length
        if (strlen($description) > 150) {
            $description = substr($description, 0, 147) . '...';
        }
        
        return $description;
    }
    
    /**
     * Categorize articles using keywords
     */
    private function categorizeArticle($content)
    {
        $content = strtolower($content);
        
        if (strpos($content, 'transfer') !== false || strpos($content, 'signing') !== false) {
            return 'transfers';
        } elseif (strpos($content, 'injury') !== false || strpos($content, 'injured') !== false) {
            return 'injuries';
        } elseif (strpos($content, 'match') !== false || strpos($content, 'game') !== false || strpos($content, 'vs') !== false) {
            return 'matches';
        } elseif (strpos($content, 'manager') !== false || strpos($content, 'coach') !== false) {
            return 'management';
        } else {
            return 'general';
        }
    }
    
    /**
     * Estimate reading time
     */
    private function estimateReadingTime($text)
    {
        $wordCount = str_word_count($text);
        $minutes = ceil($wordCount / 200); // Average reading speed
        return max(1, $minutes);
    }
    
    /**
     * Simple sentiment analysis
     */
    private function analyzeSentiment($text)
    {
        $text = strtolower($text);
        $positive = ['win', 'victory', 'success', 'goal', 'champion', 'best', 'great', 'excellent', 'amazing'];
        $negative = ['lose', 'defeat', 'injury', 'injured', 'worst', 'terrible', 'bad', 'crisis', 'problem'];
        
        $positiveCount = 0;
        $negativeCount = 0;
        
        foreach ($positive as $word) {
            $positiveCount += substr_count($text, $word);
        }
        
        foreach ($negative as $word) {
            $negativeCount += substr_count($text, $word);
        }
        
        if ($positiveCount > $negativeCount) {
            return 'positive';
        } elseif ($negativeCount > $positiveCount) {
            return 'negative';
        } else {
            return 'neutral';
        }
    }
    
    /**
     * Format date for display
     */
    private function formatDate($dateString)
    {
        try {
            $date = new \DateTime($dateString);
            return $date->format('M j, Y');
        } catch (\Exception $e) {
            return 'Recent';
        }
    }
    
    /**
     * Get default image for articles without images
     */
    private function getDefaultImage()
    {
        return 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80';
    }
    
    /**
     * Fallback news when API fails
     */
    private function getFallbackNews()
    {
        return [
            [
                'title' => 'Premier League Season Update',
                'description' => 'Stay updated with the latest Premier League news and match results.',
                'url' => 'https://premierleague.com',
                'image' => $this->getDefaultImage(),
                'source' => 'Premier League',
                'published_at' => date('M j, Y'),
                'category' => 'general',
                'reading_time' => 2,
                'sentiment' => 'neutral'
            ],
            [
                'title' => 'Fantasy Premier League Tips',
                'description' => 'Get the best tips and strategies for your Fantasy Premier League team.',
                'url' => 'https://fantasy.premierleague.com',
                'image' => $this->getDefaultImage(),
                'source' => 'FPL',
                'published_at' => date('M j, Y'),
                'category' => 'general',
                'reading_time' => 3,
                'sentiment' => 'positive'
            ]
        ];
    }
    
    /**
     * Fallback transfer news
     */
    private function getFallbackTransferNews()
    {
        return [
            [
                'title' => 'Transfer Window Updates',
                'description' => 'Latest transfer news and rumors from the Premier League.',
                'url' => 'https://premierleague.com/transfers',
                'image' => $this->getDefaultImage(),
                'source' => 'Premier League',
                'published_at' => date('M j, Y'),
                'category' => 'transfers',
                'reading_time' => 2,
                'sentiment' => 'neutral'
            ]
        ];
    }
    
    /**
     * Get trending topics using AI analysis
     */
    public function getTrendingTopics()
    {
        return Cache::remember('trending_topics', 3600, function () {
            return [
                ['name' => 'Transfer News', 'count' => rand(15, 30)],
                ['name' => 'Match Results', 'count' => rand(10, 25)],
                ['name' => 'Player Injuries', 'count' => rand(5, 15)],
                ['name' => 'Manager Updates', 'count' => rand(3, 12)],
                ['name' => 'Fantasy Tips', 'count' => rand(8, 20)]
            ];
        });
    }
}
