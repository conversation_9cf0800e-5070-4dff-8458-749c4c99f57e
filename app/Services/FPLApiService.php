<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class FPLApiService
{
    private $baseUrl = 'https://fantasy.premierleague.com/api';
    private $timeout = 30;

    /**
     * Get general FPL data (bootstrap-static)
     */
    public function getBootstrapData()
    {
        $cacheKey = 'fpl_bootstrap_data';
        
        return Cache::remember($cacheKey, 3600, function () { // 1 hour cache
            try {
                $response = Http::timeout($this->timeout)
                    ->get($this->baseUrl . '/bootstrap-static/');
                
                if ($response->successful()) {
                    return $response->json();
                }
                
                Log::error('FPL Bootstrap API Error: ' . $response->status());
                return null;
                
            } catch (Exception $e) {
                Log::error('FPL Bootstrap API Exception: ' . $e->getMessage());
                return null;
            }
        });
    }

    /**
     * Get manager summary data
     */
    public function getManagerData($managerId)
    {
        try {
            $response = Http::timeout($this->timeout)
                ->get($this->baseUrl . "/entry/{$managerId}/");
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error("FPL Manager API Error for ID {$managerId}: " . $response->status());
            return null;
            
        } catch (Exception $e) {
            Log::error("FPL Manager API Exception for ID {$managerId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get manager's history
     */
    public function getManagerHistory($managerId)
    {
        try {
            $response = Http::timeout($this->timeout)
                ->get($this->baseUrl . "/entry/{$managerId}/history/");
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error("FPL Manager History API Error for ID {$managerId}: " . $response->status());
            return null;
            
        } catch (Exception $e) {
            Log::error("FPL Manager History API Exception for ID {$managerId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get manager's team for specific gameweek
     */
    public function getManagerTeam($managerId, $gameweek = null)
    {
        try {
            // If no gameweek specified, get current gameweek
            if (!$gameweek) {
                $bootstrap = $this->getBootstrapData();
                if (!$bootstrap) return null;
                
                $currentEvent = collect($bootstrap['events'])->firstWhere('is_current', true);
                $gameweek = $currentEvent ? $currentEvent['id'] : 1;
            }
            
            $response = Http::timeout($this->timeout)
                ->get($this->baseUrl . "/entry/{$managerId}/event/{$gameweek}/picks/");
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error("FPL Manager Team API Error for ID {$managerId}, GW {$gameweek}: " . $response->status());
            return null;
            
        } catch (Exception $e) {
            Log::error("FPL Manager Team API Exception for ID {$managerId}, GW {$gameweek}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get manager's transfers
     */
    public function getManagerTransfers($managerId)
    {
        try {
            $response = Http::timeout($this->timeout)
                ->get($this->baseUrl . "/entry/{$managerId}/transfers/");
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error("FPL Manager Transfers API Error for ID {$managerId}: " . $response->status());
            return null;
            
        } catch (Exception $e) {
            Log::error("FPL Manager Transfers API Exception for ID {$managerId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get current gameweek data
     */
    public function getCurrentGameweek()
    {
        $bootstrap = $this->getBootstrapData();
        if (!$bootstrap) return null;
        
        return collect($bootstrap['events'])->firstWhere('is_current', true);
    }

    /**
     * Get all players data
     */
    public function getAllPlayers()
    {
        $bootstrap = $this->getBootstrapData();
        if (!$bootstrap) return null;
        
        return $bootstrap['elements'] ?? [];
    }

    /**
     * Get all teams data
     */
    public function getAllTeams()
    {
        $bootstrap = $this->getBootstrapData();
        if (!$bootstrap) return null;
        
        return $bootstrap['teams'] ?? [];
    }

    /**
     * Validate if manager ID exists
     */
    public function validateManagerId($managerId)
    {
        $managerData = $this->getManagerData($managerId);
        return $managerData !== null;
    }

    /**
     * Get comprehensive manager profile
     */
    public function getManagerProfile($managerId)
    {
        $managerData = $this->getManagerData($managerId);
        if (!$managerData) return null;

        $history = $this->getManagerHistory($managerId);
        $currentTeam = $this->getManagerTeam($managerId);
        $transfers = $this->getManagerTransfers($managerId);

        return [
            'manager' => $managerData,
            'history' => $history,
            'current_team' => $currentTeam,
            'transfers' => $transfers,
            'last_updated' => now()
        ];
    }

    /**
     * Format manager data for storage
     */
    public function formatManagerDataForStorage($managerData)
    {
        if (!$managerData || !isset($managerData['manager'])) {
            return null;
        }

        $manager = $managerData['manager'];
        $history = $managerData['history']['current'] ?? [];
        $currentGW = end($history);

        return [
            'fpl_manager_id' => $manager['id'],
            'fpl_team_name' => $manager['name'],
            'fpl_manager_name' => $manager['player_first_name'] . ' ' . $manager['player_last_name'],
            'fpl_total_points' => $manager['summary_overall_points'],
            'fpl_overall_rank' => $manager['summary_overall_rank'],
            'fpl_gameweek_points' => $currentGW['points'] ?? 0,
            'fpl_team_value' => ($manager['last_deadline_value'] ?? 1000) / 10, // Convert from tenths
            'fpl_bank' => ($manager['last_deadline_bank'] ?? 0) / 10, // Convert from tenths
            'fpl_data' => json_encode($managerData),
            'fpl_last_updated' => now(),
            'fpl_connected' => true
        ];
    }
}
