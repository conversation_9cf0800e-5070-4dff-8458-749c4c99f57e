# Fantasy Premier League Custom Points Calculator - User Instructions

## مرحباً بك في حاسبة النقاط المخصصة للفانتازي بريمير ليغ

هذا التطبيق يتصل بحسابك الرسمي في الفانتازي بريمير ليغ ويحسب نقاط لاعبيك حسب قوانينك المخصصة.

## الميزات الرئيسية

### 🔗 ربط مع الحساب الرسمي
- اتصال مباشر مع حسابك في Fantasy Premier League الرسمي
- جلب بيانات فريقك الحقيقي ولاعبيك المختارين
- بيانات حية ومحدثة من الموقع الرسمي

### 🎯 نظام نقاط مخصص
- حساب النقاط حسب قوانينك الخاصة
- مقارنة النقاط الرسمية مع نقاطك المخصصة
- قوانين قابلة للتخصيص لكل مركز

### 🌍 دعم متعدد اللغات
- **الإنجليزية** 🇬🇧
- **العربية** 🇸🇦 (مع دعم RTL)
- **الفرنسية** 🇫🇷

### 📊 تحليلات متقدمة
- لوحة تحكم شاملة لفريقك
- مقارنة أداء جميع اللاعبين
- إحصائيات مفصلة وتصدير البيانات

### 📱 تصميم متجاوب
- يعمل على جميع الأجهزة (كمبيوتر، تابلت، موبايل)
- تصميم يحاكي الموقع الرسمي للفانتازي بريمير ليغ

## كيفية الاستخدام

### 1. الصفحة الرئيسية
- اضغط على "Calculator" في القائمة العلوية
- أو اضغط على زر "Start Calculating" في الصفحة الرئيسية

### 2. إدخال بيانات اللاعب
1. **اسم اللاعب**: أدخل اسم اللاعب
2. **المركز**: اختر مركز اللاعب:
   - **GK**: حارس مرمى
   - **DEF**: مدافع
   - **MID**: لاعب وسط
   - **FWD**: مهاجم

### 3. إدخال الإحصائيات
- **الدقائق المُلعبة**: من 0 إلى 90 دقيقة
- **الأهداف**: عدد الأهداف المُسجلة
- **التمريرات الحاسمة**: عدد التمريرات الحاسمة
- **الشباك النظيفة**: عدد المباريات بدون استقبال أهداف
- **التصديات**: عدد التصديات (حراس المرمى فقط)
- **تصدي ضربات الجزاء**: عدد ضربات الجزاء المُتصدى لها
- **الأهداف المُستقبلة**: عدد الأهداف المُستقبلة
- **البطاقات الصفراء**: عدد البطاقات الصفراء
- **البطاقات الحمراء**: عدد البطاقات الحمراء
- **الأهداف في المرمى**: عدد الأهداف في المرمى
- **ضربات الجزاء الضائعة**: عدد ضربات الجزاء المُهدرة

### 4. عرض النتائج
- النقاط الإجمالية تظهر بخط كبير
- تفصيل مصادر النقاط يظهر أسفل النتيجة
- النقاط الإيجابية باللون الأخضر
- النقاط السلبية باللون الأحمر

## قوانين النقاط

### النقاط الأساسية
- **المشاركة**: 1 نقطة (1-59 دقيقة)، 2 نقطة (60+ دقيقة)
- **التمريرات الحاسمة**: 3 نقاط لجميع المراكز

### نقاط الأهداف (حسب المركز)
- **حراس المرمى**: 6 نقاط لكل هدف
- **المدافعين**: 6 نقاط لكل هدف
- **لاعبي الوسط**: 5 نقاط لكل هدف
- **المهاجمين**: 4 نقاط لكل هدف

### الشباك النظيفة (حسب المركز)
- **حراس المرمى**: 4 نقاط
- **المدافعين**: 4 نقاط
- **لاعبي الوسط**: 1 نقطة
- **المهاجمين**: 0 نقطة

### نقاط خاصة بحراس المرمى
- **التصديات**: 1 نقطة لكل 3 تصديات
- **تصدي ضربات الجزاء**: 5 نقاط لكل تصدي
- **الأهداف المُستقبلة**: -1 نقطة لكل هدفين مُستقبلين

### النقاط السلبية (جميع المراكز)
- **البطاقة الصفراء**: -1 نقطة
- **البطاقة الحمراء**: -3 نقطة
- **الهدف في المرمى**: -2 نقطة
- **ضربة الجزاء الضائعة**: -2 نقطة

## تغيير اللغة

### طريقة تغيير اللغة:
1. اضغط على قائمة اللغات في أعلى الصفحة
2. اختر اللغة المطلوبة:
   - 🇬🇧 English
   - 🇸🇦 العربية
   - 🇫🇷 Français

### ملاحظات مهمة:
- عند اختيار العربية، يتغير اتجاه النص إلى RTL
- جميع النصوص والأزرار تتغير للغة المختارة
- اللغة المختارة تُحفظ في الجلسة

## صفحة القوانين

للاطلاع على جميع قوانين النقاط بالتفصيل:
1. اضغط على "Rules" في القائمة العلوية
2. ستجد شرح مفصل لجميع قوانين النقاط
3. القوانين مُقسمة حسب المراكز

## نصائح للاستخدام

### للحصول على أفضل تجربة:
1. **استخدم أرقام واقعية**: لا تدخل أرقام مبالغ فيها
2. **تحقق من المركز**: تأكد من اختيار المركز الصحيح للاعب
3. **راجع النتائج**: تحقق من تفصيل النقاط للتأكد من صحة الحساب
4. **استخدم الموبايل**: التطبيق يعمل بشكل ممتاز على الهواتف الذكية

### أمثلة عملية:

#### مثال 1: محمد صلاح (مهاجم)
- الدقائق: 90
- الأهداف: 2
- التمريرات الحاسمة: 1
- **النتيجة**: 13 نقطة (2 مشاركة + 8 أهداف + 3 تمريرات حاسمة)

#### مثال 2: فيرجيل فان دايك (مدافع)
- الدقائق: 90
- الأهداف: 1
- الشباك النظيفة: 1
- البطاقات الصفراء: 1
- **النتيجة**: 11 نقطة (2 مشاركة + 6 أهداف + 4 شباك نظيفة - 1 بطاقة صفراء)

#### مثال 3: أليسون بيكر (حارس مرمى)
- الدقائق: 90
- الشباك النظيفة: 1
- التصديات: 6
- تصدي ضربات الجزاء: 1
- **النتيجة**: 13 نقطة (2 مشاركة + 4 شباك نظيفة + 2 تصديات + 5 تصدي ضربة جزاء)

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من إدخال جميع البيانات المطلوبة
2. تحقق من اتصال الإنترنت
3. جرب تحديث الصفحة
4. تأكد من أن المتصفح يدعم JavaScript

## متطلبات النظام

### المتصفحات المدعومة:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### الأجهزة المدعومة:
- أجهزة الكمبيوتر (Windows, Mac, Linux)
- الأجهزة اللوحية (iPad, Android tablets)
- الهواتف الذكية (iPhone, Android)

---

**استمتع بحساب نقاط الفانتازي بريمير ليغ!** ⚽🏆
