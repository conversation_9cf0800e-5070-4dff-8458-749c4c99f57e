/**
 * RTL Support Styles
 */

/* General RTL Adjustments */
[dir="rtl"] {
    text-align: right;
    direction: rtl;
    unicode-bidi: embed;
}

/* Navbar RTL Adjustments */
[dir="rtl"] .navbar-nav {
    padding-right: 0;
}

[dir="rtl"] .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

[dir="rtl"] .dropdown-menu {
    text-align: right;
    left: auto;
    right: 0;
}

/* Form Controls RTL Adjustments */
[dir="rtl"] .form-control {
    text-align: right;
    direction: rtl;
}

[dir="rtl"] .input-group .form-control:not(:last-child) {
    border-radius: 0 0.25rem 0.25rem 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

[dir="rtl"] .input-group-text:first-child {
    border-radius: 0 0.25rem 0.25rem 0;
}

[dir="rtl"] .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-right: -1px;
    margin-left: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

/* Card RTL Adjustments */
[dir="rtl"] .card-header {
    text-align: right;
}

/* List Groups RTL Adjustments */
[dir="rtl"] .list-group {
    padding-right: 0;
}

/* Margin and Padding RTL Adjustments */
[dir="rtl"] .me-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

[dir="rtl"] .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

[dir="rtl"] .me-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
}

[dir="rtl"] .me-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

[dir="rtl"] .me-3 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
}

[dir="rtl"] .me-4 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
}

[dir="rtl"] .me-5 {
    margin-right: 0 !important;
    margin-left: 3rem !important;
}

[dir="rtl"] .ms-1 {
    margin-left: 0 !important;
    margin-right: 0.25rem !important;
}

[dir="rtl"] .ms-2 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
}

[dir="rtl"] .ms-3 {
    margin-left: 0 !important;
    margin-right: 1rem !important;
}

[dir="rtl"] .ms-4 {
    margin-left: 0 !important;
    margin-right: 1.5rem !important;
}

[dir="rtl"] .ms-5 {
    margin-left: 0 !important;
    margin-right: 3rem !important;
}

/* Float RTL Adjustments */
[dir="rtl"] .float-start {
    float: right !important;
}

[dir="rtl"] .float-end {
    float: left !important;
}

/* Text Alignment RTL Adjustments */
[dir="rtl"] .text-start {
    text-align: right !important;
}

[dir="rtl"] .text-end {
    text-align: left !important;
}

/* Fix for Bootstrap Grid in RTL */
[dir="rtl"] .offset-1 { margin-right: 8.33333%; margin-left: 0; }
[dir="rtl"] .offset-2 { margin-right: 16.66667%; margin-left: 0; }
[dir="rtl"] .offset-3 { margin-right: 25%; margin-left: 0; }
[dir="rtl"] .offset-4 { margin-right: 33.33333%; margin-left: 0; }
[dir="rtl"] .offset-5 { margin-right: 41.66667%; margin-left: 0; }
[dir="rtl"] .offset-6 { margin-right: 50%; margin-left: 0; }
[dir="rtl"] .offset-7 { margin-right: 58.33333%; margin-left: 0; }
[dir="rtl"] .offset-8 { margin-right: 66.66667%; margin-left: 0; }
[dir="rtl"] .offset-9 { margin-right: 75%; margin-left: 0; }
[dir="rtl"] .offset-10 { margin-right: 83.33333%; margin-left: 0; }
[dir="rtl"] .offset-11 { margin-right: 91.66667%; margin-left: 0; }

/* Fix for Bootstrap Flex in RTL */
[dir="rtl"] .flex-row { flex-direction: row-reverse !important; }
[dir="rtl"] .flex-row-reverse { flex-direction: row !important; }

/* Fix for Bootstrap Buttons in RTL */
[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .btn-group > .btn:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

/* Fix for Font Awesome icons in RTL */
[dir="rtl"] .fa, 
[dir="rtl"] .fas, 
[dir="rtl"] .far, 
[dir="rtl"] .fal, 
[dir="rtl"] .fab {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Fix for RTL specific issues */
[dir="rtl"] .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
}

[dir="rtl"] .modal-header .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
}

/* Fix for Bootstrap Carousel in RTL */
[dir="rtl"] .carousel-item-next.carousel-item-start,
[dir="rtl"] .active.carousel-item-end {
    transform: translateX(-100%);
}

[dir="rtl"] .carousel-item-prev.carousel-item-end,
[dir="rtl"] .active.carousel-item-start {
    transform: translateX(100%);
}

/* Fix for Bootstrap Pagination in RTL */
[dir="rtl"] .page-item:first-child .page-link {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .page-item:last-child .page-link {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}