/**
 * Additional RTL Fixes for specific components
 * Enhanced fixes for better RTL support
 */

/* Fix for navbar in RTL */
[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

[dir="rtl"] .navbar-nav {
    padding-right: 0;
}

[dir="rtl"] .navbar-toggler {
    margin-right: 0.5rem;
    margin-left: 0;
}

[dir="rtl"] .navbar-collapse {
    text-align: right;
}

/* Fix for calculator form in RTL */
[dir="rtl"] .calculator-form label {
    text-align: right;
}

[dir="rtl"] .calculator-form .input-group .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
}

[dir="rtl"] .calculator-form .input-group .input-group-text {
    border-radius: 0.25rem 0 0 0.25rem;
}

[dir="rtl"] .calculator-form .form-floating > label {
    right: 0;
    left: auto;
    transform-origin: top right;
}

[dir="rtl"] .calculator-form .form-floating > .form-control {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

[dir="rtl"] .calculator-results {
    text-align: right;
}

[dir="rtl"] .calculator-results .result-item {
    flex-direction: row-reverse;
}

/* Fix for hero section in RTL */
[dir="rtl"] .hero-content {
    text-align: right;
}

/* Fix for news cards in RTL */
[dir="rtl"] .news-card {
    text-align: right;
}

[dir="rtl"] .news-card .card-img-top {
    /* Removing transform to avoid flipping images which can look unnatural */
    /* transform: scaleX(-1); */
}

[dir="rtl"] .news-card .card-body {
    text-align: right;
}

[dir="rtl"] .news-card .card-footer {
    text-align: right;
}

[dir="rtl"] .news-card .news-meta {
    flex-direction: row-reverse;
}

[dir="rtl"] .news-card .news-meta i {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Fix for features section in RTL */
[dir="rtl"] .feature-card {
    text-align: right;
}

/* Fix for footer in RTL */
[dir="rtl"] .footer-links {
    text-align: right;
}

[dir="rtl"] .footer-links ul {
    padding-right: 0;
}

[dir="rtl"] .footer-links li i {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .footer-newsletter {
    text-align: right;
}

[dir="rtl"] .footer-newsletter .input-group .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
}

[dir="rtl"] .footer-newsletter .input-group .btn {
    border-radius: 0.25rem 0 0 0.25rem;
}

[dir="rtl"] .footer-social a {
    margin-right: 0;
    margin-left: 1rem;
}

/* Fix for auth pages in RTL */
[dir="rtl"] .auth-card {
    text-align: right;
}

[dir="rtl"] .auth-card .card-header {
    text-align: right;
}

[dir="rtl"] .auth-card .card-body {
    text-align: right;
}

/* Fix for profile page in RTL */
[dir="rtl"] .profile-card {
    text-align: right;
}

[dir="rtl"] .profile-card .profile-avatar {
    margin-right: 0;
    margin-left: 1rem;
}

[dir="rtl"] .profile-stats {
    text-align: right;
}

[dir="rtl"] .profile-stats .stat-item {
    border-right: none;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

[dir="rtl"] .profile-stats .stat-item:last-child {
    border-left: none;
}

[dir="rtl"] .profile-tabs .nav-tabs {
    padding-right: 0;
}

[dir="rtl"] .profile-history .history-item {
    border-right: 3px solid var(--fpl-green);
    border-left: none;
    padding-right: 1rem;
    padding-left: 0;
}

/* Fix for team formation in RTL */
[dir="rtl"] .team-formation {
    direction: rtl;
}

[dir="rtl"] .team-formation .player-card {
    text-align: right;
}

[dir="rtl"] .team-formation .player-stats {
    flex-direction: row-reverse;
}

[dir="rtl"] .team-formation .player-actions {
    right: auto;
    left: 0;
}

[dir="rtl"] .team-formation .formation-controls {
    flex-direction: row-reverse;
}

/* Fix for tables in RTL */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

[dir="rtl"] .table-responsive {
    direction: rtl;
}

[dir="rtl"] .table .dropdown-menu {
    right: auto;
    left: 0;
    text-align: right;
}

[dir="rtl"] .table .sorting::before,
[dir="rtl"] .table .sorting::after {
    right: auto;
    left: 0.5em;
}

/* Fix for modal dialogs in RTL */
[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-footer {
    flex-direction: row-reverse;
}

/* Fix for alerts in RTL */
[dir="rtl"] .alert {
    text-align: right;
}

[dir="rtl"] .alert-dismissible {
    padding-right: 1rem;
    padding-left: 4rem;
}

[dir="rtl"] .alert-dismissible .btn-close {
    right: auto;
    left: 0;
}

/* Fix for breadcrumbs in RTL */
[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-right: 0;
    padding-left: 0.5rem;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item {
    padding-right: 0.5rem;
    padding-left: 0;
}

/* Fix for float elements in RTL */
[dir="rtl"] .float-start {
    float: right !important;
}

[dir="rtl"] .float-end {
    float: left !important;
}

/* Fix for text alignment classes in RTL */
[dir="rtl"] .text-start {
    text-align: right !important;
}

[dir="rtl"] .text-end {
    text-align: left !important;
}

/* Fix for Bootstrap buttons with icons in RTL */
[dir="rtl"] .btn i {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Fix for card components in RTL */
[dir="rtl"] .card-header .dropdown {
    float: left;
}

/* Fix for list groups in RTL */
[dir="rtl"] .list-group {
    padding-right: 0;
}

[dir="rtl"] .list-group-item {
    text-align: right;
}

/* Fix for dropdown menus in RTL */
[dir="rtl"] .dropdown-menu {
    text-align: right;
}

[dir="rtl"] .dropdown-item {
    text-align: right;
}

/* Fix for pagination in RTL */
[dir="rtl"] .pagination {
    padding-right: 0;
}

[dir="rtl"] .page-item:not(:first-child) .page-link {
    margin-right: -1px;
    margin-left: 0;
}

[dir="rtl"] .page-item:first-child .page-link {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .page-item:last-child .page-link {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

/* Fix for tabs in RTL */
[dir="rtl"] .nav-tabs {
    padding-right: 0;
}

[dir="rtl"] .nav-tabs .nav-item {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .nav-tabs .nav-link {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

/* Fix for form elements in RTL */
[dir="rtl"] .form-check {
    padding-left: 0;
    padding-right: 1.5em;
}

[dir="rtl"] .form-check .form-check-input {
    float: right;
    margin-left: 0;
    margin-right: -1.5em;
}

[dir="rtl"] .form-select {
    background-position: left 0.75rem center;
    padding-right: 0.75rem;
    padding-left: 2.25rem;
}

/* Fix for input groups in RTL */
[dir="rtl"] .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-right: -1px;
    margin-left: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

[dir="rtl"] .input-group > :not(:last-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

/* Fix for button groups in RTL */
[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

[dir="rtl"] .btn-group > .btn:not(:first-child) {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    margin-right: -1px;
    margin-left: 0;
}

/* Fix for grid system in RTL */
[dir="rtl"] .offset-1 { margin-left: 0; margin-right: 8.333333%; }
[dir="rtl"] .offset-2 { margin-left: 0; margin-right: 16.666667%; }
[dir="rtl"] .offset-3 { margin-left: 0; margin-right: 25%; }
[dir="rtl"] .offset-4 { margin-left: 0; margin-right: 33.333333%; }
[dir="rtl"] .offset-5 { margin-left: 0; margin-right: 41.666667%; }
[dir="rtl"] .offset-6 { margin-left: 0; margin-right: 50%; }
[dir="rtl"] .offset-7 { margin-left: 0; margin-right: 58.333333%; }
[dir="rtl"] .offset-8 { margin-left: 0; margin-right: 66.666667%; }
[dir="rtl"] .offset-9 { margin-left: 0; margin-right: 75%; }
[dir="rtl"] .offset-10 { margin-left: 0; margin-right: 83.333333%; }
[dir="rtl"] .offset-11 { margin-left: 0; margin-right: 91.666667%; }

/* Fix for margin and padding utility classes in RTL */
[dir="rtl"] .ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-5 {
    margin-right: 3rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-4 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-5 {
    margin-left: 3rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ps-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .ps-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .ps-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .ps-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .ps-5 {
    padding-right: 3rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .pe-1 {
    padding-left: 0.25rem !important;
    padding-right: 0 !important;
}

[dir="rtl"] .pe-2 {
    padding-left: 0.5rem !important;
    padding-right: 0 !important;
}

[dir="rtl"] .pe-3 {
    padding-left: 1rem !important;
    padding-right: 0 !important;
}

[dir="rtl"] .pe-4 {
    padding-left: 1.5rem !important;
    padding-right: 0 !important;
}

[dir="rtl"] .pe-5 {
    padding-left: 3rem !important;
    padding-right: 0 !important;
}