/**
 * Authentication Pages Custom Styles
 */

/* Card animation */
.card.animate__fadeIn {
    animation-duration: 0.8s;
}

/* Form input focus effect */
.form-control:focus {
    border-color: #4a6cf7;
    box-shadow: 0 0 0 0.25rem rgba(74, 108, 247, 0.25);
    transition: all 0.3s ease;
}

/* Button hover effects */

/* Submit button hover effect */
button[type="submit"] {
    transition: all 0.3s ease;
}

button[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Card show animation */
.card.show {
    opacity: 1;
    transform: translateY(0);
}

/* Initial card state for animation */
.card {
    opacity: 0.9;
    transform: translateY(10px);
    transition: all 0.5s ease;
}

/* Media query for mobile devices */
@media (max-width: 576px) {
    .card-header h3 {
        font-size: 1.5rem;
    }
    
    .card-header p {
        font-size: 0.9rem;
    }
}