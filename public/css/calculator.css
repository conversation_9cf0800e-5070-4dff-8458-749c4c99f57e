/* Additional Calculator Styles */

/* Loading Animation */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Form Enhancements */
.form-control:focus,
.form-select:focus {
    border-color: var(--fpl-green);
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 133, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--fpl-purple);
    margin-bottom: 0.5rem;
}

/* Results Animations */
.result-card {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.breakdown-item {
    animation: fadeInLeft 0.3s ease-out;
    animation-fill-mode: both;
}

.breakdown-item:nth-child(1) { animation-delay: 0.1s; }
.breakdown-item:nth-child(2) { animation-delay: 0.2s; }
.breakdown-item:nth-child(3) { animation-delay: 0.3s; }
.breakdown-item:nth-child(4) { animation-delay: 0.4s; }
.breakdown-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Button Hover Effects */
.btn-fpl-primary:active,
.btn-fpl-secondary:active,
.btn-fpl-success:active {
    transform: translateY(0);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .fpl-card {
        margin-bottom: 1rem;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .total-points {
        font-size: 2.5rem;
    }
    
    .breakdown-item {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .fpl-card {
        background: #2d3748;
        color: white;
    }
    
    .form-control,
    .form-select {
        background: #4a5568;
        border-color: #718096;
        color: white;
    }
    
    .form-control:focus,
    .form-select:focus {
        background: #4a5568;
        border-color: var(--fpl-green);
        color: white;
    }
}

/* Print Styles */
@media print {
    .navbar-fpl,
    .footer,
    .btn {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0 !important;
    }
    
    .fpl-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Accessibility Improvements */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--fpl-green);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-fpl-primary,
    .btn-fpl-secondary,
    .btn-fpl-success {
        border: 2px solid currentColor;
    }
    
    .breakdown-item.positive {
        border-left-color: #008000;
    }
    
    .breakdown-item.negative {
        border-left-color: #ff0000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Custom Scrollbar */
.rules-list::-webkit-scrollbar {
    width: 8px;
}

.rules-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.rules-list::-webkit-scrollbar-thumb {
    background: var(--fpl-purple);
    border-radius: 4px;
}

.rules-list::-webkit-scrollbar-thumb:hover {
    background: var(--fpl-green);
}

/* Tooltip Styles */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--fpl-purple);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip-custom:hover::after {
    opacity: 1;
}

/* Error States */
.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--fpl-pink);
    box-shadow: 0 0 0 0.2rem rgba(255, 0, 80, 0.25);
}

.invalid-feedback {
    color: var(--fpl-pink);
    font-weight: 500;
}

/* Success States */
.form-control.is-valid,
.form-select.is-valid {
    border-color: var(--fpl-green);
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 133, 0.25);
}

.valid-feedback {
    color: var(--fpl-green);
    font-weight: 500;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Utility Classes */
.text-fpl-purple { color: var(--fpl-purple) !important; }
.text-fpl-green { color: var(--fpl-green) !important; }
.text-fpl-pink { color: var(--fpl-pink) !important; }
.text-fpl-cyan { color: var(--fpl-cyan) !important; }

.bg-fpl-purple { background-color: var(--fpl-purple) !important; }
.bg-fpl-green { background-color: var(--fpl-green) !important; }
.bg-fpl-pink { background-color: var(--fpl-pink) !important; }
.bg-fpl-cyan { background-color: var(--fpl-cyan) !important; }
