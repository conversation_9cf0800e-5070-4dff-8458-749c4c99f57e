/**
 * نظام التحديثات في الوقت الفعلي
 * Real-time Updates System for Fantasy Premier League
 */

class RealTimeUpdates {
    constructor() {
        this.updateInterval = 30000; // 30 ثانية
        this.pointsUpdateCallbacks = [];
        this.teamUpdateCallbacks = [];
        this.playerUpdateCallbacks = [];
        this.isActive = false;
        
        this.init();
    }
    
    init() {
        // بدء النظام عند تحميل الصفحة
        this.start();
        
        // إيقاف النظام عند إغلاق الصفحة
        window.addEventListener('beforeunload', () => {
            this.stop();
        });
        
        // استئناف النظام عند العودة للصفحة
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stop();
            } else {
                this.start();
            }
        });
    }
    
    start() {
        if (this.isActive) return;
        
        this.isActive = true;
        this.updateLoop();
        console.log('Real-time updates started');
    }
    
    stop() {
        this.isActive = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        console.log('Real-time updates stopped');
    }
    
    updateLoop() {
        if (!this.isActive) return;
        
        this.intervalId = setInterval(() => {
            if (this.isActive) {
                this.checkForUpdates();
            }
        }, this.updateInterval);
    }
    
    async checkForUpdates() {
        try {
            // فحص تحديثات النقاط
            await this.checkPointsUpdates();
            
            // فحص تحديثات الفرق
            await this.checkTeamUpdates();
            
            // فحص تحديثات اللاعبين
            await this.checkPlayerUpdates();
            
        } catch (error) {
            console.error('Error checking for updates:', error);
        }
    }
    
    async checkPointsUpdates() {
        // فحص إذا كان المستخدم في صفحة تحتاج تحديث النقاط
        if (this.shouldCheckPoints()) {
            try {
                const response = await fetch('/api/user/stats', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.notifyPointsUpdate(data);
                }
            } catch (error) {
                console.error('Error fetching points updates:', error);
            }
        }
    }
    
    async checkTeamUpdates() {
        if (this.shouldCheckTeams()) {
            try {
                const response = await fetch('/api/user/teams', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.notifyTeamUpdate(data);
                }
            } catch (error) {
                console.error('Error fetching team updates:', error);
            }
        }
    }
    
    async checkPlayerUpdates() {
        if (this.shouldCheckPlayers()) {
            try {
                const response = await fetch('/api/players', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.notifyPlayerUpdate(data);
                }
            } catch (error) {
                console.error('Error fetching player updates:', error);
            }
        }
    }
    
    shouldCheckPoints() {
        // فحص إذا كانت الصفحة تحتوي على عناصر النقاط
        return document.querySelector('#totalPoints, #teamsCount, #averagePoints') !== null;
    }
    
    shouldCheckTeams() {
        // فحص إذا كانت الصفحة تحتوي على قائمة الفرق
        return document.querySelector('#teamsList, #teamsContainer') !== null;
    }
    
    shouldCheckPlayers() {
        // فحص إذا كانت الصفحة تحتوي على قائمة اللاعبين
        return document.querySelector('#playersContainer, #playersTableBody') !== null;
    }
    
    notifyPointsUpdate(data) {
        // تحديث عناصر النقاط في الصفحة
        this.updateElement('#totalPoints', data.total_points);
        this.updateElement('#teamsCount', data.teams_count);
        this.updateElement('#averagePoints', data.average_points);
        this.updateElement('#remainingBudget', data.remaining_budget?.toFixed(1));
        
        // استدعاء callbacks المسجلة
        this.pointsUpdateCallbacks.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('Error in points update callback:', error);
            }
        });
        
        // إظهار إشعار بصري
        this.showUpdateNotification('تم تحديث النقاط', 'info');
    }
    
    notifyTeamUpdate(data) {
        // استدعاء callbacks المسجلة
        this.teamUpdateCallbacks.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('Error in team update callback:', error);
            }
        });
    }
    
    notifyPlayerUpdate(data) {
        // استدعاء callbacks المسجلة
        this.playerUpdateCallbacks.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('Error in player update callback:', error);
            }
        });
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element && element.textContent != value) {
            // تأثير بصري للتحديث
            element.style.transition = 'all 0.3s ease';
            element.style.transform = 'scale(1.1)';
            element.style.color = '#00ff85';
            
            setTimeout(() => {
                element.textContent = value;
                element.style.transform = 'scale(1)';
                element.style.color = '';
            }, 150);
        }
    }
    
    showUpdateNotification(message, type = 'info') {
        // إنشاء إشعار بصري صغير
        const notification = document.createElement('div');
        notification.className = `update-notification update-${type}`;
        notification.innerHTML = `
            <i class="fas fa-sync-alt me-2"></i>
            ${message}
        `;
        
        // إضافة الإشعار للصفحة
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // تسجيل callbacks للتحديثات
    onPointsUpdate(callback) {
        this.pointsUpdateCallbacks.push(callback);
    }
    
    onTeamUpdate(callback) {
        this.teamUpdateCallbacks.push(callback);
    }
    
    onPlayerUpdate(callback) {
        this.playerUpdateCallbacks.push(callback);
    }
    
    // إزالة callbacks
    removePointsUpdateCallback(callback) {
        const index = this.pointsUpdateCallbacks.indexOf(callback);
        if (index > -1) {
            this.pointsUpdateCallbacks.splice(index, 1);
        }
    }
    
    removeTeamUpdateCallback(callback) {
        const index = this.teamUpdateCallbacks.indexOf(callback);
        if (index > -1) {
            this.teamUpdateCallbacks.splice(index, 1);
        }
    }
    
    removePlayerUpdateCallback(callback) {
        const index = this.playerUpdateCallbacks.indexOf(callback);
        if (index > -1) {
            this.playerUpdateCallbacks.splice(index, 1);
        }
    }
    
    // تحديث فوري لعنصر معين
    forceUpdate(type) {
        switch (type) {
            case 'points':
                this.checkPointsUpdates();
                break;
            case 'teams':
                this.checkTeamUpdates();
                break;
            case 'players':
                this.checkPlayerUpdates();
                break;
            case 'all':
                this.checkForUpdates();
                break;
        }
    }
}

// إنشاء instance عام
window.realTimeUpdates = new RealTimeUpdates();

// CSS للإشعارات
const notificationStyles = `
    .update-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #38003c, #4a0e4e);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        z-index: 9999;
        font-size: 14px;
        font-weight: 500;
    }
    
    .update-notification.show {
        transform: translateX(0);
    }
    
    .update-notification.update-info {
        border-left: 4px solid #00ff85;
    }
    
    .update-notification.update-success {
        border-left: 4px solid #00d4ff;
    }
    
    .update-notification.update-warning {
        border-left: 4px solid #ff0050;
    }
`;

// إضافة CSS للصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);
