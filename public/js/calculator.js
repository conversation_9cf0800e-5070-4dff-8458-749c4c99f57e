/**
 * Fantasy Premier League Points Calculator
 * JavaScript functionality for dynamic calculations
 */

class FPLCalculator {
    constructor() {
        this.form = document.getElementById('calculatorForm');
        this.resultsPanel = document.getElementById('resultsPanel');
        this.isCalculating = false;
        
        this.init();
    }
    
    init() {
        if (!this.form) return;
        
        // Bind events
        this.bindEvents();
        
        // Set up CSRF token for AJAX requests
        this.setupCSRF();
        
        // Initialize tooltips if available
        this.initTooltips();
    }
    
    bindEvents() {
        // Form submission
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.calculatePoints();
        });
        
        // Real-time calculation on input change
        const inputs = this.form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('input', this.debounce(() => {
                if (this.isFormValid()) {
                    this.calculatePoints();
                }
            }, 500));
        });
        
        // Reset button
        const resetBtn = document.querySelector('[onclick="resetForm()"]');
        if (resetBtn) {
            resetBtn.removeAttribute('onclick');
            resetBtn.addEventListener('click', () => this.resetForm());
        }
    }
    
    setupCSRF() {
        const token = document.querySelector('meta[name="csrf-token"]');
        if (token) {
            this.csrfToken = token.getAttribute('content');
        }
    }
    
    initTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }
    
    isFormValid() {
        const playerName = this.form.querySelector('#player_name').value.trim();
        const position = this.form.querySelector('#position').value;
        
        return playerName && position;
    }
    
    async calculatePoints() {
        if (this.isCalculating) return;
        
        this.isCalculating = true;
        this.showLoading();
        
        try {
            const formData = new FormData(this.form);
            
            // استخدام المسار الكامل بدلاً من المسار النسبي
            const calculatorUrl = window.location.origin + '/calculator/calculate';
            const response = await fetch(calculatorUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.displayResults(data.data);
                this.showAlert(data.message, 'success');
            } else {
                this.showError('Invalid input data');
            }
            
        } catch (error) {
            console.error('Calculation error:', error);
            this.showError('An error occurred during calculation');
        } finally {
            this.isCalculating = false;
        }
    }
    
    showLoading() {
        if (!this.resultsPanel) return;
        
        this.resultsPanel.innerHTML = `
            <div class="fpl-card text-center">
                <div class="card-body p-5">
                    <div class="loading-spinner mb-3"></div>
                    <h5>Calculating...</h5>
                    <p class="text-muted">Please wait while we calculate the points</p>
                </div>
            </div>
        `;
    }
    
    displayResults(result) {
        if (!this.resultsPanel) return;
        
        const breakdown = result.breakdown;
        let breakdownHtml = '';
        
        // Generate breakdown HTML
        if (Array.isArray(breakdown)) {
            // Handle array format
            breakdown.forEach((item, index) => {
                if (item && item.value) {
                    const value = item.value.replace(/[^0-9\-+]/g, '');
                    const numValue = parseInt(value);
                    const className = item.type || (numValue > 0 ? 'positive' : (numValue < 0 ? 'negative' : 'neutral'));
                    
                    breakdownHtml += `
                        <div class="breakdown-item ${className}" style="animation-delay: ${index * 0.1}s">
                            <span>
                                <i class="${item.icon || 'fas fa-circle'} me-2"></i>
                                ${item.label || ''}
                            </span>
                            <span class="fw-bold">${item.value}</span>
                        </div>
                    `;
                }
            });
        } else {
            // Handle object format (backward compatibility)
            Object.keys(breakdown).forEach((key, index) => {
                const value = breakdown[key];
                if (value !== 0) {
                    const className = value > 0 ? 'positive' : (value < 0 ? 'negative' : 'neutral');
                    const icon = this.getBreakdownIcon(key);
                    const label = this.getBreakdownLabel(key);
                    
                    breakdownHtml += `
                        <div class="breakdown-item ${className}" style="animation-delay: ${index * 0.1}s">
                            <span>
                                <i class="${icon} me-2"></i>
                                ${label}
                            </span>
                            <span class="fw-bold">${value > 0 ? '+' : ''}${value}</span>
                        </div>
                    `;
                }
            });
        }
        
        if (!breakdownHtml) {
            breakdownHtml = '<p class="text-muted text-center">No points to display</p>';
        }
        
        const resultsHtml = `
            <div class="fpl-card">
                <div class="fpl-card-header text-center">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        Results
                    </h5>
                </div>
                <div class="card-body">
                    <div class="result-card">
                        <h6 class="mb-2">${this.escapeHtml(result.player_name)}</h6>
                        <div class="position-badge mb-2">
                            <span class="badge bg-light text-dark">${result.position}</span>
                        </div>
                        <div class="total-points">${result.total_points}</div>
                        <p class="mb-0">Total Points</p>
                    </div>
                    
                    <h6 class="fw-bold mb-3 mt-4">Points Breakdown</h6>
                    <div class="breakdown-list">
                        ${breakdownHtml}
                    </div>
                </div>
            </div>
        `;
        
        this.resultsPanel.innerHTML = resultsHtml;
    }
    
    getBreakdownIcon(key) {
        const icons = {
            'appearance': 'fas fa-clock',
            'goals': 'fas fa-futbol',
            'assists': 'fas fa-hands-helping',
            'clean_sheets': 'fas fa-shield-alt',
            'saves': 'fas fa-hand-paper',
            'penalty_saves': 'fas fa-hand-rock',
            'goals_conceded': 'fas fa-arrow-down',
            'yellow_cards': 'fas fa-square text-warning',
            'red_cards': 'fas fa-square text-danger',
            'own_goals': 'fas fa-times-circle',
            'penalty_misses': 'fas fa-times'
        };
        return icons[key] || 'fas fa-circle';
    }
    
    getBreakdownLabel(key) {
        const labels = {
            'appearance': 'Appearance Points',
            'goals': 'Goal Points',
            'assists': 'Assist Points',
            'clean_sheets': 'Clean Sheet Points',
            'saves': 'Save Points',
            'penalty_saves': 'Penalty Save Points',
            'goals_conceded': 'Goals Conceded',
            'yellow_cards': 'Yellow Card Points',
            'red_cards': 'Red Card Points',
            'own_goals': 'Own Goal Points',
            'penalty_misses': 'Penalty Miss Points'
        };
        return labels[key] || key;
    }
    
    resetForm() {
        if (!this.form) return;
        
        this.form.reset();
        
        if (this.resultsPanel) {
            this.resultsPanel.innerHTML = `
                <div class="fpl-card text-center">
                    <div class="card-body p-5">
                        <i class="fas fa-calculator fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">Results</h5>
                        <p class="text-muted">Enter player information to calculate points</p>
                    </div>
                </div>
            `;
        }
        
        // Clear any alerts
        this.clearAlerts();
    }
    
    showAlert(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        const icon = type === 'success' ? 'fa-check-circle' : 
                    type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show`;
        alert.setAttribute('role', 'alert');
        alert.innerHTML = `
            <i class="fas ${icon} me-2"></i>
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    }
    
    showError(message) {
        this.showAlert(message, 'error');
        
        if (this.resultsPanel) {
            this.resultsPanel.innerHTML = `
                <div class="fpl-card text-center">
                    <div class="card-body p-5">
                        <i class="fas fa-exclamation-triangle fa-4x text-danger mb-3"></i>
                        <h5 class="text-danger">Error</h5>
                        <p class="text-muted">${this.escapeHtml(message)}</p>
                    </div>
                </div>
            `;
        }
    }
    
    clearAlerts() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => alert.remove());
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize calculator when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new FPLCalculator();
});

// Global functions for backward compatibility
function resetForm() {
    const calculator = new FPLCalculator();
    calculator.resetForm();
}

function calculatePoints() {
    const calculator = new FPLCalculator();
    calculator.calculatePoints();
}
