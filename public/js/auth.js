/**
 * Authentication Pages Dynamic Effects
 * Enhanced version with more interactive elements
 * Optimized for mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add fade-in effect to the card
    const authCard = document.querySelector('.card');
    if (authCard) {
        setTimeout(() => {
            authCard.classList.add('show');
        }, 150);
    }
    
    // Check if device is mobile
    const isMobile = window.innerWidth <= 576;
    
    // Google login button animations removed
    
    // Add animation to form inputs
    const formInputs = document.querySelectorAll('.form-control');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('input-focused');
            this.style.borderColor = '#4a6cf7';
            this.style.boxShadow = '0 0 0 0.25rem rgba(74, 108, 247, 0.25)';
            this.style.transition = 'all 0.3s ease';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('input-focused');
            this.style.borderColor = '';
            this.style.boxShadow = '';
        });
    });
    
    // Add animation to submit buttons
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(button => {
        // For desktop devices only
        if (!isMobile) {
            button.addEventListener('mouseover', function() {
                this.classList.add('shadow-lg');
                this.style.transform = 'translateY(-2px)';
                this.style.transition = 'all 0.3s ease';
            });
            
            button.addEventListener('mouseout', function() {
                this.classList.remove('shadow-lg');
                this.style.transform = 'translateY(0)';
            });
        }
        
        // For all devices - touch effect
        button.addEventListener('touchstart', function() {
            this.classList.add('shadow-lg');
            this.style.transform = 'scale(0.98)';
        });
        
        button.addEventListener('touchend', function() {
            this.classList.remove('shadow-lg');
            this.style.transform = 'scale(1)';
        });
    });
    
    // Handle window resize for responsive design
    window.addEventListener('resize', function() {
        const newIsMobile = window.innerWidth <= 576;
        if (newIsMobile !== isMobile) {
            // Reload page to apply correct mobile/desktop styles
            window.location.reload();
        }
    });
});