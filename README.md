# Fantasy Premier League - Official Website

A modern, responsive website for Fantasy Premier League with AI-powered news, multi-language support, and stunning user interface. Built with <PERSON><PERSON> and featuring real-time Premier League news integration.

## 🌟 Features

### 🎨 **Modern Design**
- Stunning hero carousel with dynamic content
- Responsive design that works on all devices
- Official Fantasy Premier League color scheme
- Smooth animations and transitions
- Professional UI/UX with Bootstrap 5

### 🌍 **Multi-Language Support**
- **English** 🇬🇧 (Default)
- **Arabic** 🇸🇦 (with full RTL support)
- **French** 🇫🇷
- Dynamic language switching with session persistence
- Fully translated interface and content

### 📰 **AI-Powered News**
- Real-time Premier League news integration
- AI-enhanced article processing and categorization
- Sentiment analysis for news articles
- Transfer news and match updates
- Trending topics with smart algorithms
- News from trusted sources (BBC, Sky Sports, ESPN)

### ⚡ **Interactive Features**
- Hero slider with multiple slides
- "Register Now" call-to-action buttons
- "Watch Demo" modal with video integration
- Smooth scrolling navigation
- Scroll-to-top functionality
- Loading animations and transitions

### 📱 **Responsive Experience**
- Mobile-first design approach
- Touch-friendly interface
- Optimized for all screen sizes
- Fast loading times
- Progressive enhancement

## 🚀 Quick Start

### Prerequisites
- PHP 8.1 or higher
- Composer
- MySQL or compatible database

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Fantasy_PL
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database configuration**
   Update your `.env` file:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=fantasy_pl
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

5. **Run migrations**
   ```bash
   php artisan migrate
   ```

6. **Start the server**
   ```bash
   php artisan serve
   ```

Visit `http://localhost:8000` to access the application.

## 🎮 How to Use

### 1. Connect Your FPL Account
1. Visit the homepage and click "Connect FPL Account"
2. Enter your FPL Team ID (found in your team URL)
3. The system will verify and connect your account

### 2. View Your Dashboard
- See your team with official vs custom points
- Compare individual player performances
- View overall team statistics and rankings

### 3. Explore All Players
- Browse all Premier League players
- Compare official and custom points for any player
- Filter by team, position, or search by name
- Sort by different metrics

### 4. Customize Scoring Rules
Edit `config/custom_rules.php` to modify:
- Point values for goals, assists, clean sheets
- Position-specific scoring multipliers
- Penalty point deductions
- Bonus point systems

## 🔧 Configuration

### Custom Scoring Rules

The scoring system is fully configurable in `config/custom_rules.php`:

```php
'scoring' => [
    'goals' => [
        'goalkeeper' => 6,
        'defender' => 6,
        'midfielder' => 5,
        'forward' => 4,
    ],
    'assists' => [
        'all_positions' => 3,
    ],
    // ... more rules
],
```

### Language Configuration

Add new languages in `config/languages.php` and create corresponding translation files in `resources/lang/`.

## 🏗️ Architecture

### Backend Components
- **FPLApiService**: Handles all FPL API communications
- **CalculatorService**: Processes custom point calculations
- **FPLController**: Manages user interactions and data flow
- **SetLocale Middleware**: Handles multi-language support

### Frontend Features
- Bootstrap 5 with RTL support
- Real-time AJAX updates
- Responsive design patterns
- Custom CSS with FPL theming

### Key Files
```
app/
├── Services/
│   ├── FPLApiService.php      # FPL API integration
│   └── CalculatorService.php   # Custom points calculation
├── Http/Controllers/
│   └── FPLController.php       # Main application controller
resources/
├── views/fpl/                  # FPL-specific views
├── lang/                       # Multi-language support
config/
├── custom_rules.php            # Custom scoring configuration
└── languages.php               # Language configuration
```

## 🔌 API Integration

### FPL API Endpoints Used
- `/bootstrap-static/` - General game data, players, teams
- `/entry/{user_id}/` - User team information
- `/entry/{user_id}/event/{gw}/picks/` - Team picks for gameweek
- `/event/{gw}/live/` - Live gameweek data

### Rate Limiting & Caching
- 5-minute cache for static data
- 1-minute cache for live data
- Respectful API usage with proper timeouts

## 🎨 Customization

### Adding New Scoring Rules
1. Edit `config/custom_rules.php`
2. Add new rule categories or modify existing ones
3. Update the `CalculatorService` if needed
4. Add translations for new rule descriptions

### Styling Changes
- Main styles: `resources/views/layouts/calculator.blade.php`
- Additional CSS: `public/css/calculator.css`
- Use CSS variables for easy color customization

### Adding New Languages
1. Create translation file: `resources/lang/{locale}/app.php`
2. Add language to `config/languages.php`
3. Update language selector in layout

## 📊 Data Flow

1. **User connects FPL account** → System fetches user data
2. **FPL API provides player stats** → Real-time data retrieval
3. **Custom rules applied** → Points calculated using your rules
4. **Results displayed** → Comparison with official points
5. **Real-time updates** → Automatic data refresh

## 🔒 Privacy & Security

- No FPL login credentials stored
- Only public team data accessed
- Session-based user identification
- Secure API communication with timeouts
- No personal data collection beyond team ID

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📱 Mobile Experience

- Fully responsive design
- Touch-friendly interface
- Optimized for mobile viewing
- Fast loading on mobile networks

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is open-sourced software licensed under the [MIT license](LICENSE).

## 🙏 Acknowledgments

- Fantasy Premier League for the official API
- Laravel framework for the robust backend
- Bootstrap for the responsive frontend
- The FPL community for inspiration

## 📞 Support

For issues or questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information
4. Contact the development team

---

**Enjoy your custom Fantasy Premier League experience!** ⚽🏆
