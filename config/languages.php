<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Supported Languages
    |--------------------------------------------------------------------------
    |
    | This array contains all the languages that the application supports.
    | Each language should have a corresponding language file in the
    | resources/lang directory.
    |
    */
    
    'supported' => [
        'en' => [
            'name' => 'English',
            'flag' => '🇬🇧',
            'dir' => 'ltr',
        ],
        'ar' => [
            'name' => 'العربية',
            'flag' => '🇸🇦',
            'dir' => 'rtl',
        ],
        'fr' => [
            'name' => 'Français',
            'flag' => '🇫🇷',
            'dir' => 'ltr',
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Default Language
    |--------------------------------------------------------------------------
    |
    | This is the default language that will be used when no language
    | is specified or when the requested language is not supported.
    |
    */
    
    'default' => 'en',
    
    /*
    |--------------------------------------------------------------------------
    | Fallback Language
    |--------------------------------------------------------------------------
    |
    | This is the fallback language that will be used when a translation
    | key is not found in the current language.
    |
    */
    
    'fallback' => 'en',
];
